/**
 * License Manager for Facebook Group Leaver Extension
 * Handles Gumroad license key verification and premium status management
 */

class LicenseManager {
  constructor() {
    // TODO: Replace with your actual Gumroad product ID
    this.productId = 'facebook-group-leaver-premium'; // Placeholder - replace with actual product ID
    this.gumroadApiUrl = 'https://api.gumroad.com/v2/licenses/verify';
    this.licenseKey = null;
    this.isVerified = false;
    this.lastVerificationTime = null;
    this.verificationCacheTime = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  /**
   * Initialize license manager and verify stored license
   */
  async initialize() {
    try {
      await this.loadStoredLicense();
      if (this.licenseKey) {
        await this.verifyLicense(this.licenseKey, false); // Don't increment uses on startup
      }
    } catch (error) {
      console.error('License manager initialization failed:', error);
    }
  }

  /**
   * Load license information from chrome storage
   */
  async loadStoredLicense() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['licenseKey', 'licenseVerified', 'lastVerificationTime'], (result) => {
        if (chrome.runtime.lastError) {
          console.error('Error loading license from storage:', chrome.runtime.lastError);
          resolve();
          return;
        }

        this.licenseKey = result.licenseKey || null;
        this.isVerified = result.licenseVerified || false;
        this.lastVerificationTime = result.lastVerificationTime || null;
        
        console.log('Loaded license from storage:', {
          hasKey: !!this.licenseKey,
          isVerified: this.isVerified,
          lastVerification: this.lastVerificationTime
        });
        
        resolve();
      });
    });
  }

  /**
   * Save license information to chrome storage
   */
  async saveLicenseData() {
    return new Promise((resolve) => {
      const data = {
        licenseKey: this.licenseKey,
        licenseVerified: this.isVerified,
        lastVerificationTime: this.lastVerificationTime
      };

      chrome.storage.local.set(data, () => {
        if (chrome.runtime.lastError) {
          console.error('Error saving license to storage:', chrome.runtime.lastError);
        } else {
          console.log('License data saved to storage');
        }
        resolve();
      });
    });
  }

  /**
   * Verify license key with Gumroad API
   */
  async verifyLicense(licenseKey, incrementUses = true) {
    if (!licenseKey || !licenseKey.trim()) {
      throw new Error('License key is required');
    }

    // Check if we have a recent verification to avoid unnecessary API calls
    if (this.isVerified && this.lastVerificationTime && 
        (Date.now() - this.lastVerificationTime) < this.verificationCacheTime &&
        this.licenseKey === licenseKey) {
      console.log('Using cached license verification');
      return {
        success: true,
        cached: true,
        isVerified: this.isVerified
      };
    }

    try {
      console.log('Verifying license with Gumroad API...');
      
      const formData = new FormData();
      formData.append('product_id', this.productId);
      formData.append('license_key', licenseKey.trim());
      formData.append('increment_uses_count', incrementUses ? 'true' : 'false');

      const response = await fetch(this.gumroadApiUrl, {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        // License is valid
        this.licenseKey = licenseKey.trim();
        this.isVerified = true;
        this.lastVerificationTime = Date.now();
        
        await this.saveLicenseData();
        
        console.log('License verification successful:', {
          uses: data.uses,
          productName: data.purchase?.product_name
        });

        return {
          success: true,
          isVerified: true,
          uses: data.uses,
          purchase: data.purchase
        };
      } else {
        // License is invalid
        this.isVerified = false;
        this.lastVerificationTime = Date.now();
        
        // Don't clear the license key immediately - let user try again
        await this.saveLicenseData();
        
        console.log('License verification failed:', data.message);
        
        return {
          success: false,
          isVerified: false,
          message: data.message || 'Invalid license key'
        };
      }
    } catch (error) {
      console.error('License verification error:', error);
      
      // On network error, keep existing verification status if we have one
      if (this.isVerified && this.licenseKey === licenseKey) {
        console.log('Network error, but keeping existing verified status');
        return {
          success: true,
          isVerified: true,
          cached: true,
          networkError: true
        };
      }
      
      throw new Error('Unable to verify license. Please check your internet connection and try again.');
    }
  }

  /**
   * Activate a new license key
   */
  async activateLicense(licenseKey) {
    try {
      const result = await this.verifyLicense(licenseKey, false); // Don't increment on activation
      
      if (result.success && result.isVerified) {
        return {
          success: true,
          message: 'License activated successfully! Premium features are now available.'
        };
      } else {
        return {
          success: false,
          message: result.message || 'Invalid license key. Please check your key and try again.'
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Deactivate current license
   */
  async deactivateLicense() {
    this.licenseKey = null;
    this.isVerified = false;
    this.lastVerificationTime = null;
    
    await this.saveLicenseData();
    
    return {
      success: true,
      message: 'License deactivated successfully.'
    };
  }

  /**
   * Get current license status
   */
  getLicenseStatus() {
    return {
      hasLicense: !!this.licenseKey,
      isVerified: this.isVerified,
      licenseKey: this.licenseKey,
      lastVerificationTime: this.lastVerificationTime
    };
  }

  /**
   * Check if user has premium access
   */
  isPremium() {
    return this.isVerified;
  }

  /**
   * Get masked license key for display (show only first 8 and last 4 characters)
   */
  getMaskedLicenseKey() {
    if (!this.licenseKey || this.licenseKey.length < 12) {
      return this.licenseKey;
    }
    
    const start = this.licenseKey.substring(0, 8);
    const end = this.licenseKey.substring(this.licenseKey.length - 4);
    const middle = '*'.repeat(Math.max(0, this.licenseKey.length - 12));
    
    return `${start}${middle}${end}`;
  }

  /**
   * Detect license key from clipboard
   */
  async detectLicenseFromClipboard() {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        const clipboardText = await navigator.clipboard.readText();
        
        // Basic license key pattern detection (adjust based on Gumroad's format)
        const licensePattern = /^[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}-[A-F0-9]{8}$/i;
        
        if (licensePattern.test(clipboardText.trim())) {
          return clipboardText.trim();
        }
      }
    } catch (error) {
      console.log('Clipboard access not available or denied');
    }
    
    return null;
  }

  /**
   * Get Gumroad purchase URL
   */
  getPurchaseUrl() {
    return 'https://hweihwang.gumroad.com/l/facebook-groups-leaver';
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LicenseManager;
}
