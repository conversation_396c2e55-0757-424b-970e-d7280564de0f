// Enhanced Facebook Group Leaver Content Script

// --- DOM SELECTORS ---
const GROUP_LIST_ITEMS_CONTAINER_SELECTOR = 'div[role="main"] div[role="list"]';
const GROUP_ITEM_SELECTOR = 'div[role="main"] div[role="list"] > div[role="listitem"]';

// Multiple selectors for group names with fallbacks
// Based on actual Facebook HTML structure analysis
const GROUP_NAME_SELECTORS = [
  'a[href*="/groups/"][tabindex="0"]',                   // Primary: matches the provided HTML structure
  'a[href*="/groups/"][role="link"]',                    // Fallback 1: link with role and groups href
  'a[href*="/groups/"]:not([aria-label])',              // Fallback 2: link with groups href, no aria-label
  'a[href*="/groups/"]',                                 // Fallback 3: any link with groups href
  'a[role="link"][href*="/groups/"]',                    // Fallback 4: role first, then href
  'a[href^="https://www.facebook.com/groups/"]',        // Fallback 5: full Facebook groups URL
  'div[role="listitem"] a[href*="/groups/"]:first-of-type', // Fallback 6: first groups link in list item
  'div[role="listitem"] a:first-of-type',               // Fallback 7: first link in list item
  'a[class*="x1i10hfl"][href*="/groups/"]',             // Fallback 8: Facebook's common link class
  'a[class*="x1ejq31n"][href*="/groups/"]',             // Fallback 9: Another Facebook link class
  // Additional selectors for different Facebook layouts
  'h3 a[href*="/groups/"]',                             // Fallback 10: Group name in h3 heading
  'h4 a[href*="/groups/"]',                             // Fallback 11: Group name in h4 heading
  'span a[href*="/groups/"]',                           // Fallback 12: Group name in span
  'div[data-testid] a[href*="/groups/"]',               // Fallback 13: Group name in testid container
  'strong a[href*="/groups/"]',                         // Fallback 14: Group name in strong tag
  '[role="heading"] a[href*="/groups/"]'                // Fallback 15: Group name in heading role
];

const OPTIONS_BUTTON_SELECTOR = 'div[aria-label="More"][role="button"]';
const LEAVE_GROUP_MENU_ITEM_SELECTOR = 'div[role="menuitem"]';
const CONFIRM_LEAVE_BUTTON_SELECTOR = 'div[aria-label="Leave Group"][role="button"], button';

// Modal selectors for the "report group" modal that appears after leaving
const REPORT_GROUP_MODAL_SELECTOR = 'div[role="dialog"]';
const MODAL_HEADER_TEXT_SELECTOR = 'h2, h3, div[role="heading"]';
const MODAL_OVERLAY_SELECTOR = 'div[data-pagelet="root"]';

class GroupLeaverProcessor {
  constructor() {
    this.isProcessing = false;
    this.isPaused = false;
    this.shouldStop = false;
    this.currentSettings = null;
    this.processStats = {
      total: 0,
      processed: 0,
      left: 0,
      skipped: 0,
      failed: 0
    };
    this.retryQueue = [];
    this.groupsToProcess = [];
    this.autoScrollEnabled = false; // Will be set based on user tier and settings
    this.lastScrollPosition = 0;
    this.leftGroups = []; // Track successfully left groups
    this.skippedGroups = []; // Track skipped groups with reasons
    this.failedGroups = []; // Track failed groups with reasons

    // Enhanced UX controls
    this.skipCurrentDelay = false;
    this.manualMode = false;
    this.waitingForUser = false;
    this.currentDelayType = null;
    this.delayStartTime = null;
    this.backgroundMode = false;
    this.processingStartTime = null;
    this.stopAutoDiscovery = false; // Flag to stop auto-discovery process

    console.log("Enhanced Facebook Group Leaver content script loaded");

    // Initialize page monitoring and communication
    this.initializePageMonitoring();

    // Auto-detect page readiness
    this.detectPageReadiness();

    // Listen for visibility changes for background processing
    this.setupBackgroundProcessing();

    // Announce readiness to background script
    this.announceReadiness();
  }

  // --- PAGE MONITORING AND COMMUNICATION ---
  initializePageMonitoring() {
    this.currentUrl = window.location.href;
    this.lastGroupCount = 0;
    this.pageObserver = null;
    this.urlCheckInterval = null;

    // Set up URL change detection
    this.setupUrlChangeDetection();

    // Set up DOM mutation observer for dynamic content
    this.setupDOMObserver();

    // Set up periodic checks for content changes
    this.setupContentChangeDetection();
  }

  setupUrlChangeDetection() {
    // Listen for popstate events (back/forward navigation)
    window.addEventListener('popstate', () => {
      this.handleUrlChange();
    });

    // Monitor URL changes via polling (for SPA navigation)
    this.urlCheckInterval = setInterval(() => {
      if (window.location.href !== this.currentUrl) {
        this.handleUrlChange();
      }
    }, 1000);

    // Override pushState and replaceState to catch programmatic navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      originalPushState.apply(history, args);
      setTimeout(() => this.handleUrlChange(), 100);
    };

    history.replaceState = (...args) => {
      originalReplaceState.apply(history, args);
      setTimeout(() => this.handleUrlChange(), 100);
    };
  }

  setupDOMObserver() {
    // Observe changes to the main content area
    const targetNode = document.body;

    this.pageObserver = new MutationObserver((mutations) => {
      let hasSignificantChanges = false;

      mutations.forEach((mutation) => {
        // Check for added nodes that might be group items
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if this could be a group item or container
              if (this.couldBeGroupContent(node)) {
                hasSignificantChanges = true;
              }
            }
          });
        }
      });

      if (hasSignificantChanges) {
        this.handleContentChange();
      }
    });

    this.pageObserver.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: false
    });
  }

  setupContentChangeDetection() {
    // Periodic check for group count changes
    setInterval(() => {
      const currentGroupCount = document.querySelectorAll(GROUP_ITEM_SELECTOR).length;
      if (currentGroupCount !== this.lastGroupCount) {
        this.lastGroupCount = currentGroupCount;
        this.handleContentChange();
      }
    }, 2000);
  }

  couldBeGroupContent(node) {
    // Check if the node could contain group information
    const groupIndicators = [
      'group', 'join', 'leave', 'member', 'admin',
      '[role="article"]', '[data-pagelet]', '[data-testid]'
    ];

    const nodeText = node.textContent?.toLowerCase() || '';
    const nodeHTML = node.outerHTML?.toLowerCase() || '';

    return groupIndicators.some(indicator =>
      nodeText.includes(indicator) || nodeHTML.includes(indicator)
    );
  }

  handleUrlChange() {
    const newUrl = window.location.href;
    const oldUrl = this.currentUrl;
    this.currentUrl = newUrl;

    console.log(`URL changed from ${oldUrl} to ${newUrl}`);

    // Reset page state
    this.lastGroupCount = 0;

    // Notify background script of URL change
    chrome.runtime.sendMessage({
      action: 'urlChanged',
      oldUrl: oldUrl,
      newUrl: newUrl,
      isGroupsPage: this.isGroupsPage(newUrl)
    });

    // Re-detect page readiness after URL change
    setTimeout(() => {
      this.detectPageReadiness();
    }, 500);
  }

  handleContentChange() {
    console.log('Content change detected');

    // Notify background script of content changes
    chrome.runtime.sendMessage({
      action: 'pageContentChanged',
      data: {
        groupCount: document.querySelectorAll(GROUP_ITEM_SELECTOR).length,
        url: window.location.href,
        timestamp: Date.now()
      }
    });
  }

  isGroupsPage(url) {
    return url && (
      url.includes('facebook.com/groups/') ||
      url.includes('facebook.com/groups?') ||
      url.includes('facebook.com/groups#')
    );
  }

  announceReadiness() {
    // Get immediate group count for auto-detection
    const currentGroupCount = document.querySelectorAll(GROUP_ITEM_SELECTOR).length;
    const isGroupsPage = this.isGroupsPage(window.location.href);

    console.log(`Announcing readiness: Groups page: ${isGroupsPage}, Group count: ${currentGroupCount}`);

    // Announce to background script that content script is ready with group info
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      url: window.location.href,
      isGroupsPage: isGroupsPage,
      groupCount: currentGroupCount,
      timestamp: Date.now()
    });

    // If we're on a groups page and have groups, trigger additional readiness checks
    if (isGroupsPage && currentGroupCount > 0) {
      console.log(`Auto-detected ${currentGroupCount} groups - ready for immediate scanning`);
    }
  }

  // --- HELPER FUNCTIONS ---
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Enhanced sleep with skip capability and progress reporting
  async smartDelay(ms, delayType = 'general', reason = '') {
    if (this.manualMode) {
      return this.waitForUserAction(delayType, reason);
    }

    this.currentDelayType = delayType;
    this.delayStartTime = Date.now();
    this.skipCurrentDelay = false;

    // Send delay start notification to popup
    this.sendDelayUpdate(delayType, reason, ms);

    return new Promise((resolve) => {
      const checkInterval = 100; // Check every 100ms for skip
      let elapsed = 0;

      const intervalId = setInterval(() => {
        elapsed += checkInterval;

        // Check if user wants to skip
        if (this.skipCurrentDelay || this.shouldStop) {
          clearInterval(intervalId);
          this.currentDelayType = null;
          this.sendDelayUpdate('completed', '', 0);
          resolve();
          return;
        }

        // Check if delay is complete
        if (elapsed >= ms) {
          clearInterval(intervalId);
          this.currentDelayType = null;
          this.sendDelayUpdate('completed', '', 0);
          resolve();
          return;
        }

        // Send progress update
        const remaining = ms - elapsed;
        this.sendDelayUpdate(delayType, reason, remaining);
      }, checkInterval);
    });
  }

  async waitForUserAction(delayType, reason) {
    this.waitingForUser = true;
    this.sendDelayUpdate('manual', `${delayType}: ${reason}`, 0);

    return new Promise((resolve) => {
      const checkInterval = 500;
      const intervalId = setInterval(() => {
        if (!this.waitingForUser || this.shouldStop) {
          clearInterval(intervalId);
          this.sendDelayUpdate('completed', '', 0);
          resolve();
        }
      }, checkInterval);
    });
  }

  setupBackgroundProcessing() {
    // Handle visibility changes for background processing
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isProcessing) {
        this.backgroundMode = true;
        console.log('Switched to background processing mode');
        this.sendPopupMessage('Background processing enabled - you can switch tabs');
      } else if (!document.hidden && this.backgroundMode) {
        this.backgroundMode = false;
        console.log('Returned to foreground processing mode');
        this.sendPopupMessage('Foreground processing resumed');
      }
    });
  }

  sendDelayUpdate(type, reason, remainingMs) {
    chrome.runtime.sendMessage({
      action: "delayUpdate",
      data: {
        type: type,
        reason: reason,
        remaining: remainingMs,
        canSkip: !this.manualMode,
        isManual: this.manualMode
      }
    });
  }

  async clickElement(element, description = "") {
    if (element) {
      console.log(`Clicking: ${description || element.textContent?.trim() || element.outerHTML.substring(0, 70)}`);
      if (this.currentSettings?.autoScroll && this.currentSettings?.isPremium) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // Reduced delay with smart detection
        await this.smartDelay(300, 'scroll', 'Scrolling element into view');
      }
      element.click();
      return true;
    }
    console.warn(`Element not found for click: ${description}`);
    return false;
  }

  sendPopupMessage(text, action = "updateStatus") {
    chrome.runtime.sendMessage({ action: action, text: text });
  }

  sendProgressUpdate() {
    chrome.runtime.sendMessage({
      action: "updateProgress",
      data: { ...this.processStats }
    });
  }

  sendCurrentAction(text) {
    chrome.runtime.sendMessage({ action: "currentAction", text: text });
  }

  // Auto-detect if the page is ready for group processing
  detectPageReadiness() {
    const checkReadiness = () => {
      const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);
      if (groupItems.length > 0) {
        console.log(`Page ready: Found ${groupItems.length} group items`);

        // Immediately notify about page readiness with group count
        this.notifyPageReadiness(groupItems.length);
        return true;
      }
      return false;
    };

    // Check immediately
    if (checkReadiness()) return;

    // Check periodically for up to 10 seconds with more frequent checks
    let attempts = 0;
    const maxAttempts = 30; // Increased attempts
    const interval = setInterval(() => {
      attempts++;
      if (checkReadiness() || attempts >= maxAttempts) {
        clearInterval(interval);
        if (attempts >= maxAttempts) {
          console.log("Page readiness timeout - no groups found");
          // Still notify even if no groups found
          this.notifyPageReadiness(0);
        }
      }
    }, 333); // Faster checking (3 times per second)
  }

  // New method to notify popup about page readiness
  notifyPageReadiness(groupCount) {
    console.log(`Notifying page readiness: ${groupCount} groups detected`);

    chrome.runtime.sendMessage({
      action: 'pageReadinessDetected',
      data: {
        groupCount: groupCount,
        url: window.location.href,
        isGroupsPage: this.isGroupsPage(window.location.href),
        timestamp: Date.now()
      }
    });
  }

  // Enhanced auto-scroll functionality with smart delays and stop capability
  async autoScrollToLoadGroups() {
    if (!this.autoScrollEnabled) return;

    const initialGroupCount = document.querySelectorAll(GROUP_ITEM_SELECTOR).length;
    let currentGroupCount = initialGroupCount;
    let scrollAttempts = 0;
    const maxScrollAttempts = 3; // Reduced from 5 to prevent long waits
    const maxConsecutiveFailures = 2; // Stop after 2 consecutive failures

    console.log(`Starting auto-scroll. Initial groups: ${initialGroupCount}`);
    this.stopAutoDiscovery = false; // Reset stop flag

    while (scrollAttempts < maxScrollAttempts && !this.stopAutoDiscovery) {
      // Scroll to bottom
      const previousScrollHeight = document.body.scrollHeight;
      window.scrollTo(0, document.body.scrollHeight);

      // Shorter delay with early exit detection
      await this.smartScrollDelay(currentGroupCount);

      // Check if stop was requested during delay
      if (this.stopAutoDiscovery) {
        console.log('Auto-discovery stopped by user');
        break;
      }

      // Check if new groups loaded
      const newGroupCount = document.querySelectorAll(GROUP_ITEM_SELECTOR).length;
      const newScrollHeight = document.body.scrollHeight;

      if (newGroupCount > currentGroupCount) {
        console.log(`Auto-scroll loaded ${newGroupCount - currentGroupCount} more groups`);
        currentGroupCount = newGroupCount;
        scrollAttempts = 0; // Reset attempts since we found new content
      } else if (newScrollHeight === previousScrollHeight) {
        // No new content and scroll height didn't change - likely at end
        scrollAttempts++;
        console.log(`Auto-scroll attempt ${scrollAttempts}/${maxScrollAttempts} - no new content, scroll height unchanged`);

        if (scrollAttempts >= maxConsecutiveFailures) {
          console.log('Detected end of content - stopping auto-discovery');
          break;
        }
      } else {
        scrollAttempts++;
        console.log(`Auto-scroll attempt ${scrollAttempts}/${maxScrollAttempts} - no new groups`);
      }
    }

    console.log(`Auto-scroll complete. Final groups: ${currentGroupCount}`);
    return currentGroupCount;
  }

  // Smart scroll delay that adapts based on content loading with stop capability
  async smartScrollDelay(currentGroupCount) {
    const maxWaitTime = 1500; // Reduced from 3000ms to 1.5 seconds for faster response
    const checkInterval = 100; // Check every 100ms for more responsive detection
    let elapsed = 0;

    while (elapsed < maxWaitTime && !this.stopAutoDiscovery) {
      // Use simple sleep instead of smartDelay to avoid "Waiting for new groups to load" message
      await this.sleep(checkInterval);

      // Check if stop was requested
      if (this.stopAutoDiscovery) {
        console.log('Auto-discovery stopped during scroll delay');
        return;
      }

      // Check if new content appeared
      const newCount = document.querySelectorAll(GROUP_ITEM_SELECTOR).length;
      if (newCount > currentGroupCount) {
        console.log('New content detected, ending scroll delay early');
        return;
      }

      elapsed += checkInterval;
    }
  }

  // --- HELPER METHOD FOR GROUP NAME EXTRACTION ---
  // Extract both group name and URL from DOM element
  extractGroupInfo(groupItem, index) {
    console.log(`Extracting group info for item ${index}...`);

    // First check if this might be a loading/placeholder element
    if (this.isLoadingElement(groupItem)) {
      console.log(`⏳ Item ${index} appears to be loading, skipping for now`);
      return null; // Return null to indicate this should be skipped, not counted as "Unknown"
    }

    let groupName = null;
    let groupUrl = null;

    // Try each selector in order until we find a group name and URL
    for (let i = 0; i < GROUP_NAME_SELECTORS.length; i++) {
      const selector = GROUP_NAME_SELECTORS[i];
      try {
        const nameElements = groupItem.querySelectorAll(selector);

        // Try all matching elements, not just the first one
        for (const nameElement of nameElements) {
          if (nameElement) {
            // Try multiple text extraction methods
            const extractedText = this.extractTextFromElement(nameElement);
            if (extractedText && extractedText.length > 2) {
              console.log(`✅ Found group name "${extractedText}" using selector ${i + 1}: ${selector}`);
              groupName = extractedText;

              // Extract URL from the same element if it's a link
              if (nameElement.href) {
                groupUrl = nameElement.href;
                console.log(`✅ Found group URL: "${groupUrl}"`);
              }

              return { name: groupName, url: groupUrl };
            }
          }
        }

        if (nameElements.length > 0) {
          console.log(`❌ Selector ${i + 1} found ${nameElements.length} elements but no usable text: ${selector}`);
        }
      } catch (error) {
        console.warn(`❌ Selector ${i + 1} failed: ${selector}`, error);
        continue;
      }
    }

    console.log(`⚠️ No selectors worked, trying enhanced text content extraction...`);

    // Enhanced text content extraction with better filtering
    const extractedName = this.extractNameFromTextContent(groupItem);
    if (extractedName) {
      console.log(`✅ Extracted group name from text content: "${extractedName}"`);
      groupName = extractedName;
    }

    // Try to extract URL from any group link in the item
    if (!groupUrl) {
      const groupLinks = groupItem.querySelectorAll('a[href*="/groups/"]');
      if (groupLinks.length > 0) {
        groupUrl = groupLinks[0].href;
        console.log(`✅ Found group URL from link: "${groupUrl}"`);
      }
    }

    // Try to extract from href attribute as last resort for name
    if (!groupName) {
      const hrefName = this.extractNameFromHref(groupItem);
      if (hrefName) {
        console.log(`✅ Extracted group name from href: "${hrefName}"`);
        groupName = hrefName;
      }
    }

    if (groupName) {
      return { name: groupName, url: groupUrl };
    }

    console.warn(`❌ Could not extract group info for item ${index}, excluding from results`);
    return null; // Return null to indicate this element should be excluded
  }

  // Legacy method for backward compatibility
  extractGroupName(groupItem, index) {
    const info = this.extractGroupInfo(groupItem, index);
    return info ? info.name : null;
  }

  // Check if element appears to be in a loading state
  isLoadingElement(element) {
    const loadingIndicators = [
      'loading', 'skeleton', 'placeholder', 'shimmer',
      'data-placeholder', 'aria-busy="true"'
    ];

    const elementHTML = element.outerHTML.toLowerCase();
    const elementText = element.textContent?.toLowerCase() || '';

    return loadingIndicators.some(indicator =>
      elementHTML.includes(indicator) || elementText.includes(indicator)
    );
  }

  // Enhanced text extraction from individual elements
  extractTextFromElement(element) {
    // Try different text properties
    const textSources = [
      element.textContent?.trim(),
      element.innerText?.trim(),
      element.getAttribute('aria-label')?.trim(),
      element.getAttribute('title')?.trim(),
      element.getAttribute('alt')?.trim()
    ];

    for (const text of textSources) {
      if (text && text.length > 2 && text.length < 200) {
        // Filter out common non-group-name text
        if (!this.isCommonUIText(text)) {
          return text;
        }
      }
    }
    return null;
  }

  // Enhanced text content extraction with better pattern matching
  extractNameFromTextContent(groupItem) {
    const allText = groupItem.textContent?.trim();
    if (!allText) return null;

    console.log(`Full text content: "${allText}"`);

    // Split by various delimiters and clean up
    const lines = allText.split(/[\n\r\t]+/).map(line => line.trim()).filter(line => line.length > 0);
    console.log(`Text lines found:`, lines);

    // Look for the most likely group name (usually the longest meaningful text)
    let bestCandidate = null;
    let bestScore = 0;

    for (const line of lines) {
      if (this.isCommonUIText(line)) {
        console.log(`Skipping UI text: "${line}"`);
        continue;
      }

      // Score based on length and characteristics
      const score = this.scoreGroupNameCandidate(line);
      if (score > bestScore && score > 0) {
        bestScore = score;
        bestCandidate = line;
      }
    }

    return bestCandidate;
  }

  // Extract group name from href attribute
  extractNameFromHref(groupItem) {
    const links = groupItem.querySelectorAll('a[href*="/groups/"]');
    for (const link of links) {
      const href = link.getAttribute('href');
      if (href) {
        // Try to extract group name from URL
        const match = href.match(/\/groups\/([^\/\?]+)/);
        if (match && match[1]) {
          // Decode and clean up the URL part
          const urlPart = decodeURIComponent(match[1]);
          // Convert URL-style names to readable format
          const readable = urlPart.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
          if (readable.length > 3 && readable.length < 100) {
            return readable;
          }
        }
      }
    }
    return null;
  }

  // Check if text is common UI text that shouldn't be used as group name
  isCommonUIText(text) {
    const commonTexts = [
      'member', 'members', 'post', 'posts', 'ago', 'more', 'join', 'view', 'notification',
      'notifications', 'activity', 'recent', 'new', 'today', 'yesterday', 'week', 'month',
      'admin', 'moderator', 'public', 'private', 'group', 'page', 'like', 'comment',
      'share', 'follow', 'unfollow', 'see more', 'see less', 'view group', 'view more'
    ];

    const lowerText = text.toLowerCase();
    return commonTexts.some(common => lowerText.includes(common)) ||
           text.length < 3 ||
           text.length > 200 ||
           /^\d+$/.test(text) || // Just numbers
           /^[^\w\s]+$/.test(text); // Just symbols
  }

  // Score a potential group name candidate
  scoreGroupNameCandidate(text) {
    if (!text || this.isCommonUIText(text)) return 0;

    let score = 0;

    // Length scoring (prefer medium length names)
    if (text.length >= 5 && text.length <= 50) score += 3;
    else if (text.length >= 3 && text.length <= 100) score += 1;

    // Prefer text with letters
    if (/[a-zA-Z]/.test(text)) score += 2;

    // Prefer text that looks like a proper name (has capitals)
    if (/[A-Z]/.test(text)) score += 1;

    // Prefer text with spaces (multi-word names)
    if (/\s/.test(text)) score += 1;

    // Penalize text with too many numbers or symbols
    if (/\d{3,}/.test(text)) score -= 1;
    if (/[^\w\s]{3,}/.test(text)) score -= 1;

    return score;
  }

  // --- DEBUG HELPER METHODS ---
  debugGroupExtraction() {
    console.log('=== DEBUG: Group Name Extraction ===');
    const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);
    console.log(`Found ${groupItems.length} group items`);

    groupItems.forEach((item, index) => {
      console.log(`\n--- Group Item ${index + 1} ---`);
      console.log('HTML:', item.outerHTML.substring(0, 200) + '...');

      // Test each selector
      GROUP_NAME_SELECTORS.forEach((selector, selectorIndex) => {
        try {
          const element = item.querySelector(selector);
          if (element) {
            console.log(`Selector ${selectorIndex + 1} (${selector}): Found element with text: "${element.textContent?.trim() || 'NO TEXT'}"`);
          } else {
            console.log(`Selector ${selectorIndex + 1} (${selector}): No element found`);
          }
        } catch (error) {
          console.log(`Selector ${selectorIndex + 1} (${selector}): Error - ${error.message}`);
        }
      });

      // Show final extracted name
      const extractedName = this.extractGroupName(item, index);
      console.log(`Final extracted name: "${extractedName}"`);
    });

    console.log('=== END DEBUG ===');
  }

  // New debug method to analyze DOM element structure
  debugDOMElements() {
    console.log('=== DEBUG: DOM Element Analysis ===');
    const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);
    console.log(`Found ${groupItems.length} elements with selector: ${GROUP_ITEM_SELECTOR}`);

    groupItems.forEach((item, index) => {
      console.log(`\n--- DOM Element ${index + 1} ---`);

      // Basic element info
      console.log('Tag:', item.tagName);
      console.log('Classes:', item.className);
      console.log('ID:', item.id || 'none');

      // Attributes
      const attributes = {};
      for (let attr of item.attributes) {
        attributes[attr.name] = attr.value;
      }
      console.log('Attributes:', attributes);

      // Content analysis
      console.log('Text content length:', item.textContent?.length || 0);
      console.log('Text content preview:', item.textContent?.substring(0, 100) || 'NO TEXT');
      console.log('Inner HTML length:', item.innerHTML?.length || 0);

      // Child elements
      console.log('Direct children count:', item.children.length);
      console.log('All descendants count:', item.querySelectorAll('*').length);

      // Links analysis
      const links = item.querySelectorAll('a[href*="/groups/"]');
      console.log('Group links found:', links.length);
      links.forEach((link, linkIndex) => {
        console.log(`  Link ${linkIndex + 1}: href="${link.href}", text="${link.textContent?.trim() || 'NO TEXT'}"`);
      });

      // Visibility checks
      const rect = item.getBoundingClientRect();
      console.log('Bounding rect:', {
        width: rect.width,
        height: rect.height,
        top: rect.top,
        left: rect.left,
        bottom: rect.bottom,
        right: rect.right
      });
      console.log('offsetParent:', item.offsetParent ? 'exists' : 'null');
      console.log('Display style:', window.getComputedStyle(item).display);
      console.log('Visibility style:', window.getComputedStyle(item).visibility);

      // Check for loading/placeholder indicators
      const isLoading = this.isLoadingElement(item);
      console.log('Appears to be loading element:', isLoading);

      // Full HTML (truncated)
      console.log('Full HTML (first 500 chars):', item.outerHTML.substring(0, 500) + '...');
    });

    console.log('=== END DOM DEBUG ===');
  }

  // --- GROUP SCANNING ---
  async scanGroups(enableAutoScroll = true, scanMode = 'auto') {
    // Set auto-scroll based on scan mode and enableAutoScroll parameter
    // For freemium users, enableAutoScroll will be false from popup
    this.autoScrollEnabled = enableAutoScroll && scanMode !== 'manual';

    // Handle different scanning modes based on tier
    if (scanMode === 'manual') {
      // Free tier: Manual scroll mode - scan only visible groups
      console.log("Manual scan mode: scanning visible groups only");
      this.sendPopupMessage('📋 Scanning visible groups only. Premium users get automatic discovery of all groups.', 'tierMessage');
    } else if (enableAutoScroll && this.autoScrollEnabled) {
      // Premium tier: Auto-scroll to load more groups
      console.log("Premium auto-scroll mode: discovering all groups...");
      this.sendPopupMessage('🚀 Premium auto-discovery: Loading all your groups automatically...', 'tierMessage');
      await this.autoScrollToLoadGroups();
    } else {
      // Fallback: scan visible groups without scrolling
      console.log("Standard scan mode: scanning visible groups");
    }

    const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);
    const groups = [];

    console.log(`Found ${groupItems.length} group items using selector: ${GROUP_ITEM_SELECTOR}`);

    let validGroupCount = 0;
    let skippedCount = 0;

    groupItems.forEach((groupItem, index) => {
      // Use enhanced group info extraction to get both name and URL
      const groupInfo = this.extractGroupInfo(groupItem, index);

      // Skip elements where we couldn't extract valid group info
      if (groupInfo === null) {
        skippedCount++;
        console.log(`Skipping element at index ${index} - no valid group info found`);
        return;
      }

      const groupName = groupInfo.name;
      const groupUrl = groupInfo.url;

      // Skip groups that we've already left (in case DOM removal failed or Facebook re-added them)
      if (this.leftGroups.includes(groupName)) {
        console.log(`Skipping already left group: "${groupName}"`);
        return;
      }

      // Try to detect if user is admin (lightweight text-based detection only)
      const isAdmin = this.detectAdminStatus(groupItem);

      // Extract detailed activity information for premium features
      const activityInfo = this.extractActivityInfo(groupItem);

      validGroupCount++;
      groups.push({
        name: groupName,
        url: groupUrl, // Include the actual group URL
        element: groupItem,
        isAdmin: isAdmin,
        isActive: activityInfo.isActive,
        activityInfo: activityInfo, // Include detailed activity data for premium features
        index: index
      });

      console.log(`Valid Group ${validGroupCount}: "${groupName}" (URL: ${groupUrl || 'N/A'}, Admin: ${isAdmin}, Active: ${activityInfo.isActive}, Last Visited: ${activityInfo.lastVisited || 'Unknown'})`);
    });

    console.log(`Scan summary: Found ${groupItems.length} DOM elements, extracted ${validGroupCount} valid groups, skipped ${skippedCount} invalid elements`);

    // Send tier-specific completion message
    if (scanMode === 'manual') {
      this.sendPopupMessage(`📋 Found ${groups.length} visible groups. Scroll down manually to see more, or upgrade to Premium for automatic discovery.`, 'tierMessage');
    } else if (enableAutoScroll && this.autoScrollEnabled) {
      this.sendPopupMessage(`🚀 Premium scan complete: Discovered ${groups.length} total groups automatically.`, 'tierMessage');
    }

    console.log(`Scan complete. Found ${groups.length} groups.`);
    return groups;
  }

  detectAdminStatus(groupItem) {
    // Look for admin indicators in the group item
    const adminIndicators = [
      'admin', 'moderator', 'mod', 'owner',
      'Admin', 'Moderator', 'Mod', 'Owner'
    ];

    const text = groupItem.textContent.toLowerCase();
    return adminIndicators.some(indicator => text.includes(indicator.toLowerCase()));
  }

  detectRecentActivity(groupItem) {
    const text = groupItem.textContent.toLowerCase();

    // Look for "You last visited..." patterns to determine activity status
    const lastVisitedMatch = text.match(/you last visited[^.]*?(\d+)\s*(minute|hour|day|week|month|year)s?\s*ago/i);
    if (lastVisitedMatch) {
      const timeValue = parseInt(lastVisitedMatch[1]);
      const timeUnit = lastVisitedMatch[2].toLowerCase();

      // Consider active if visited within last 7 days
      if (timeUnit === 'minute' || timeUnit === 'hour') return true;
      if (timeUnit === 'day' && timeValue <= 7) return true;
      if (timeUnit === 'week' && timeValue <= 1) return true;

      return false; // Inactive if visited longer ago
    }

    // Fallback to basic activity indicators
    const activityIndicators = [
      'new post', 'new posts', 'recent activity',
      'active today', 'active yesterday', 'notification',
      'posted', 'commented', 'shared'
    ];

    return activityIndicators.some(indicator => text.includes(indicator));
  }

  // Extract detailed activity information for premium features
  extractActivityInfo(groupItem) {
    const text = groupItem.textContent;

    // Try to extract "You last visited..." information
    const lastVisitedMatch = text.match(/You last visited[^.]*?(\d+)\s*(minute|hour|day|week|month|year)s?\s*ago/i);
    if (lastVisitedMatch) {
      const timeValue = parseInt(lastVisitedMatch[1]);
      const timeUnit = lastVisitedMatch[2].toLowerCase();

      // Convert to days for sorting purposes
      let daysAgo = 0;
      switch (timeUnit) {
        case 'minute': daysAgo = 0; break;
        case 'hour': daysAgo = 0; break;
        case 'day': daysAgo = timeValue; break;
        case 'week': daysAgo = timeValue * 7; break;
        case 'month': daysAgo = timeValue * 30; break;
        case 'year': daysAgo = timeValue * 365; break;
      }

      return {
        hasActivityInfo: true,
        lastVisited: `${timeValue} ${timeUnit}${timeValue > 1 ? 's' : ''} ago`,
        daysAgo: daysAgo,
        isActive: daysAgo <= 7
      };
    }

    // Check for other activity indicators
    const hasActivity = this.detectRecentActivity(groupItem);
    return {
      hasActivityInfo: false,
      lastVisited: null,
      daysAgo: hasActivity ? 0 : 999, // Assume recent if has activity indicators
      isActive: hasActivity
    };
  }

  // --- MAIN PROCESSING LOGIC ---
  async startLeavingGroups(groups, settings) {
    this.isProcessing = true;
    this.shouldStop = false;
    this.isPaused = false;
    this.currentSettings = settings;
    this.groupsToProcess = [...groups];
    this.processingStartTime = Date.now(); // Track start time for ETA

    // Set auto-scroll based on user tier and settings
    this.autoScrollEnabled = settings.autoScroll && settings.isPremium;

    this.processStats = {
      total: groups.length,
      processed: 0,
      left: 0,
      skipped: 0,
      failed: 0
    };
    this.leftGroups = []; // Reset left groups tracking
    this.skippedGroups = []; // Reset skipped groups tracking
    this.failedGroups = []; // Reset failed groups tracking

    console.log(`Starting enhanced group leaving process for ${groups.length} groups`);
    this.sendPopupMessage(`Starting process for ${groups.length} groups...`);

    try {
      for (let i = 0; i < this.groupsToProcess.length; i++) {
        if (this.shouldStop) {
          console.log("Process stopped by user");
          break;
        }

        // Handle pause with better UX
        while (this.isPaused && !this.shouldStop) {
          await this.smartDelay(500, 'pause', 'Process is paused - click Resume to continue');
        }

        if (this.shouldStop) break;

        const group = this.groupsToProcess[i];
        await this.processGroup(group, i + 1);

        this.processStats.processed++;
        this.sendProgressUpdate();

        // Respect batch size limit
        if (this.processStats.processed >= settings.batchSize) {
          console.log(`Reached batch size limit (${settings.batchSize})`);
          this.sendPopupMessage(`Batch limit reached (${settings.batchSize})`);
          break;
        }

        // Smart delay between groups with user control
        if (i < this.groupsToProcess.length - 1) {
          const remaining = this.groupsToProcess.length - i - 1;
          await this.smartDelay(
            settings.actionDelay,
            'action',
            `Waiting before next group (${remaining} remaining)`
          );
        }
      }

      // Process retry queue if any
      await this.processRetryQueue();

      console.log("Group leaving process completed");
      chrome.runtime.sendMessage({
        action: "processComplete",
        data: {
          ...this.processStats,
          leftGroups: this.leftGroups, // Include list of successfully left groups
          skippedGroups: this.skippedGroups, // Include list of skipped groups
          failedGroups: this.failedGroups // Include list of failed groups
        }
      });

    } catch (error) {
      console.error("Error in group leaving process:", error);
      chrome.runtime.sendMessage({
        action: "processError",
        error: error.message
      });
    } finally {
      this.isProcessing = false;
    }
  }

  async processGroup(group, groupNumber) {
    const groupName = group.name;
    console.log(`Processing group ${groupNumber}: ${groupName}`);
    this.sendCurrentAction(`Processing ${groupName} (${groupNumber}/${this.processStats.total})`);

    // Apply safety filters
    if (this.currentSettings.skipAdminGroups && group.isAdmin) {
      console.log(`Skipping ${groupName} - user is admin`);
      this.processStats.skipped++;
      return;
    }

    if (this.currentSettings.skipRecentlyActive && group.isActive) {
      console.log(`Skipping ${groupName} - recently active`);
      this.processStats.skipped++;
      return;
    }

    // Find the group element on the page
    const groupElement = this.findGroupElement(group);
    if (!groupElement) {
      console.warn(`Could not find group element for ${groupName}`);
      this.processStats.failed++;
      return;
    }

    // Attempt to leave the group with retries
    const result = await this.attemptLeaveGroup(groupElement, groupName);
    if (result === true) {
      this.processStats.left++;
      this.leftGroups.push(groupName); // Track successfully left group
      console.log(`Successfully left group: ${groupName}`);

      // Remove the group element from DOM to provide immediate visual feedback
      this.removeGroupFromDOM(groupElement, groupName);
    } else if (result === 'admin') {
      // Group was detected as admin-owned during leave attempt
      console.log(`Skipping ${groupName} - detected as admin-owned during leave attempt`);
      this.processStats.skipped++;
      this.skippedGroups.push(groupName);
    } else {
      // Add to retry queue if retries are enabled
      if (this.currentSettings.retryAttempts > 0) {
        this.retryQueue.push({ group, attempts: 0 });
      } else {
        this.processStats.failed++;
        this.failedGroups.push(groupName);
      }
    }
  }

  findGroupElement(group) {
    // Try to find the group element by name or index
    const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);

    // First try to match by name using our enhanced extraction method
    for (let i = 0; i < groupItems.length; i++) {
      const item = groupItems[i];
      const extractedName = this.extractGroupName(item, i);
      if (extractedName === group.name) {
        console.log(`Found group element for "${group.name}" by name match`);
        return item;
      }
    }

    // Fallback to index if available
    if (group.index !== undefined && groupItems[group.index]) {
      console.log(`Found group element for "${group.name}" by index ${group.index}`);
      return groupItems[group.index];
    }

    // Last resort: try partial name matching
    for (let i = 0; i < groupItems.length; i++) {
      const item = groupItems[i];
      const extractedName = this.extractGroupName(item, i);
      if (extractedName.includes(group.name) || group.name.includes(extractedName)) {
        console.log(`Found group element for "${group.name}" by partial match with "${extractedName}"`);
        return item;
      }
    }

    console.warn(`Could not find group element for "${group.name}"`);
    return null;
  }

  removeGroupFromDOM(groupElement, groupName) {
    try {
      console.log(`Removing group "${groupName}" from DOM for immediate visual feedback`);

      // Add a fade-out animation before removal for better UX
      groupElement.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
      groupElement.style.opacity = '0.3';
      groupElement.style.transform = 'scale(0.95)';

      // Add a visual indicator that this group was processed
      const processedIndicator = document.createElement('div');
      processedIndicator.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #10B981;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        z-index: 1000;
        pointer-events: none;
      `;
      processedIndicator.textContent = '✓ Left';

      // Make the group element relative positioned to contain the indicator
      groupElement.style.position = 'relative';
      groupElement.appendChild(processedIndicator);

      // Remove the element after a short delay to show the "Left" indicator
      setTimeout(() => {
        try {
          if (groupElement && groupElement.parentNode) {
            groupElement.parentNode.removeChild(groupElement);
            console.log(`Successfully removed group "${groupName}" from DOM`);
          }
        } catch (removeError) {
          console.warn(`Error removing group "${groupName}" from DOM:`, removeError);
        }
      }, 1500); // 1.5 second delay to show the indicator

    } catch (error) {
      console.warn(`Error setting up DOM removal for group "${groupName}":`, error);
      // Fallback: try immediate removal without animation
      try {
        if (groupElement && groupElement.parentNode) {
          groupElement.parentNode.removeChild(groupElement);
          console.log(`Fallback: Successfully removed group "${groupName}" from DOM`);
        }
      } catch (fallbackError) {
        console.error(`Failed to remove group "${groupName}" from DOM:`, fallbackError);
      }
    }
  }

  async attemptLeaveGroup(groupElement, groupName) {
    try {
      // Click the options button
      const optionsButton = groupElement.querySelector(OPTIONS_BUTTON_SELECTOR);
      if (!await this.clickElement(optionsButton, `Options button for ${groupName}`)) {
        console.warn(`Could not find/click options button for ${groupName}`);
        return false;
      }

      await this.smartDelay(this.currentSettings.actionDelay, 'action', 'Waiting for menu to appear');

      // Smart menu detection instead of fixed delay
      let leaveMenuItem = await this.waitForElement(
        () => this.findLeaveMenuItem(),
        2000,
        'Waiting for Leave Group menu item'
      );

      // If no "Leave group" menu item is found, this is likely an admin group
      if (!leaveMenuItem) {
        console.log(`No "Leave group" option found for ${groupName} - likely an admin-owned group, skipping`);
        // Close any open menu by pressing Escape
        const escapeEvent = new KeyboardEvent('keydown', {
          key: 'Escape',
          code: 'Escape',
          keyCode: 27,
          which: 27,
          bubbles: true,
          cancelable: true
        });
        document.dispatchEvent(escapeEvent);
        return 'admin'; // Special return value to indicate admin group
      }

      if (!await this.clickElement(leaveMenuItem, `Leave group menu item for ${groupName}`)) {
        console.warn(`Could not click "Leave group" menu item for ${groupName}`);
        return false;
      }

      await this.smartDelay(this.currentSettings.confirmDelay, 'confirm', 'Waiting for confirmation dialog');

      // Smart confirmation button detection
      let confirmButton = await this.waitForElement(
        () => this.findConfirmButton(),
        3000,
        'Waiting for confirmation button'
      );

      if (!await this.clickElement(confirmButton, `Confirm leave button for ${groupName}`)) {
        console.warn(`Could not find/click confirm leave button for ${groupName}`);
        return false;
      }

      // After clicking confirm, check for and handle the "report group" modal
      console.log(`Checking for report group modal after leaving ${groupName}...`);
      await this.smartDelay(1500, 'modal-check', 'Waiting for potential report group modal');

      const reportModal = this.detectReportGroupModal();
      if (reportModal) {
        console.log(`Report group modal detected for ${groupName}, attempting to dismiss...`);
        const modalDismissed = await this.dismissReportGroupModal(reportModal);

        if (modalDismissed) {
          console.log(`Successfully dismissed report group modal for ${groupName}`);
        } else {
          console.warn(`Failed to dismiss report group modal for ${groupName}, but continuing...`);
          // Continue anyway - don't fail the entire process for modal dismissal
        }
      } else {
        console.log(`No report group modal detected for ${groupName}`);
      }

      console.log(`Successfully left group: ${groupName}`);
      // Brief final delay to ensure everything is settled
      await this.smartDelay(500, 'cleanup', 'Finalizing group leave');
      return true;

    } catch (error) {
      console.error(`Error leaving group ${groupName}:`, error);
      return false;
    }
  }

  // Smart element detection with timeout
  async waitForElement(finder, maxWaitMs, reason) {
    const startTime = Date.now();
    const checkInterval = 100;

    while (Date.now() - startTime < maxWaitMs) {
      const element = finder();
      if (element) {
        return element;
      }
      await this.smartDelay(checkInterval, 'detection', reason);
    }
    return null;
  }

  findLeaveMenuItem() {
    let leaveMenuItem = null;
    document.querySelectorAll(LEAVE_GROUP_MENU_ITEM_SELECTOR).forEach(item => {
      if (item.offsetParent !== null &&
          (item.textContent.includes('Leave group') || item.innerText.includes('Leave group'))) {
        leaveMenuItem = item;
      }
    });
    return leaveMenuItem;
  }

  findConfirmButton() {
    let confirmButton = null;
    document.querySelectorAll(CONFIRM_LEAVE_BUTTON_SELECTOR).forEach(btn => {
      if (btn.offsetParent !== null &&
          (btn.textContent.includes('Leave Group') ||
           btn.getAttribute('aria-label')?.includes('Leave Group') ||
           btn.getAttribute('name') === 'confirmed' ||
           (btn.tagName === 'BUTTON' && btn.textContent.includes('Leave'))
          )) {
        confirmButton = btn;
      }
    });
    return confirmButton;
  }

  // Detect the "report group" modal that appears after leaving a group
  detectReportGroupModal() {
    try {
      // Method 1: Look for modal dialogs with role="dialog"
      const modals = document.querySelectorAll(REPORT_GROUP_MODAL_SELECTOR);

      for (const modal of modals) {
        // Check if modal is visible
        if (modal.offsetParent === null) continue;

        // Look for "Left group" text in the modal header
        const headerElements = modal.querySelectorAll(MODAL_HEADER_TEXT_SELECTOR);
        for (const header of headerElements) {
          const headerText = header.textContent?.trim() || '';
          if (headerText.includes('Left group') || headerText.includes('Do you want to report this group')) {
            console.log('Detected report group modal with header:', headerText);
            return modal;
          }
        }

        // Also check for the specific modal content about reporting
        const modalText = modal.textContent || '';
        if (modalText.includes('Do you want to report this group') ||
            modalText.includes('Left group') ||
            modalText.includes('Nudity or sexual activity') ||
            modalText.includes('Bullying or harassment')) {
          console.log('Detected report group modal by content text');
          return modal;
        }
      }

      // Method 2: Look for any visible overlay/modal containers that might contain the report modal
      const overlaySelectors = [
        'div[data-pagelet="root"] > div[style*="position: fixed"]',
        'div[style*="position: fixed"][style*="z-index"]',
        'div[class*="modal"]',
        'div[class*="overlay"]'
      ];

      for (const selector of overlaySelectors) {
        const overlays = document.querySelectorAll(selector);
        for (const overlay of overlays) {
          if (overlay.offsetParent === null) continue;

          const overlayText = overlay.textContent || '';
          if ((overlayText.includes('Left group') || overlayText.includes('Do you want to report this group')) &&
              overlayText.includes('Facebook')) {
            console.log('Detected report group modal in overlay container');
            return overlay;
          }
        }
      }

      return null;
    } catch (error) {
      console.warn('Error detecting report group modal:', error);
      return null;
    }
  }

  // Dismiss the report group modal by clicking outside of it
  async dismissReportGroupModal(modal) {
    try {
      console.log('Attempting to dismiss report group modal...');

      // Method 1: Try to find and click a close button (X) if it exists
      const closeButtonSelectors = [
        'div[aria-label="Close"]',
        'button[aria-label="Close"]',
        'div[role="button"][aria-label="Close"]',
        'button[data-testid="close"]',
        'div[data-testid="close"]',
        'svg[aria-label="Close"]',
        'div[aria-label*="close" i]',
        'button[aria-label*="close" i]'
      ];

      for (const selector of closeButtonSelectors) {
        const closeButton = modal.querySelector(selector);
        if (closeButton && closeButton.offsetParent !== null) {
          console.log('Found close button, clicking it');
          closeButton.click();
          await this.smartDelay(500, 'modal', 'Waiting for modal to close via close button');

          // Check if modal was dismissed
          if (!this.detectReportGroupModal()) {
            return true;
          }
        }
      }

      // Method 2: Click outside the modal (on the overlay)
      // Find the modal's parent overlay or backdrop
      const overlay = document.querySelector(MODAL_OVERLAY_SELECTOR) || document.body;

      // Get modal bounds to click outside of it
      const modalRect = modal.getBoundingClientRect();
      const overlayRect = overlay.getBoundingClientRect();

      // Calculate a point outside the modal but within the overlay
      let clickX, clickY;

      // Try clicking to the right of the modal
      if (modalRect.right + 50 < overlayRect.right) {
        clickX = modalRect.right + 25;
        clickY = modalRect.top + (modalRect.height / 2);
      }
      // Try clicking to the left of the modal
      else if (modalRect.left - 50 > overlayRect.left) {
        clickX = modalRect.left - 25;
        clickY = modalRect.top + (modalRect.height / 2);
      }
      // Try clicking above the modal
      else if (modalRect.top - 50 > overlayRect.top) {
        clickX = modalRect.left + (modalRect.width / 2);
        clickY = modalRect.top - 25;
      }
      // Try clicking below the modal
      else if (modalRect.bottom + 50 < overlayRect.bottom) {
        clickX = modalRect.left + (modalRect.width / 2);
        clickY = modalRect.bottom + 25;
      }
      // Fallback: click at a safe distance from modal center
      else {
        clickX = overlayRect.left + 50;
        clickY = overlayRect.top + 50;
      }

      console.log(`Clicking outside modal at coordinates (${clickX}, ${clickY})`);

      // Create and dispatch a click event at the calculated coordinates
      const clickEvent = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: true,
        clientX: clickX,
        clientY: clickY
      });

      // Find the element at those coordinates and click it
      const elementAtPoint = document.elementFromPoint(clickX, clickY);
      if (elementAtPoint) {
        elementAtPoint.dispatchEvent(clickEvent);
        console.log('Dispatched click event to dismiss modal');
      } else {
        // Fallback: dispatch on the overlay
        overlay.dispatchEvent(clickEvent);
        console.log('Dispatched click event on overlay to dismiss modal');
      }

      // Wait for modal to close
      await this.smartDelay(800, 'modal', 'Waiting for modal to close');

      // Verify modal was dismissed
      let modalStillVisible = this.detectReportGroupModal();
      if (!modalStillVisible) {
        console.log('Successfully dismissed report group modal');
        return true;
      }

      // Method 3: Try pressing Escape key as a last resort
      console.log('Trying Escape key to dismiss modal...');
      const escapeEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        code: 'Escape',
        keyCode: 27,
        which: 27,
        bubbles: true,
        cancelable: true
      });

      document.dispatchEvent(escapeEvent);
      modal.dispatchEvent(escapeEvent);

      await this.smartDelay(500, 'modal', 'Waiting for modal to close via Escape key');

      // Final check
      modalStillVisible = this.detectReportGroupModal();
      if (!modalStillVisible) {
        console.log('Successfully dismissed report group modal via Escape key');
        return true;
      } else {
        console.warn('Modal still visible after all dismissal attempts');
        return false;
      }

    } catch (error) {
      console.error('Error dismissing report group modal:', error);
      return false;
    }
  }

  async processRetryQueue() {
    if (this.retryQueue.length === 0) return;

    console.log(`Processing retry queue: ${this.retryQueue.length} items`);
    this.sendCurrentAction(`Retrying failed groups...`);

    const itemsToRetry = [...this.retryQueue];
    this.retryQueue = [];

    for (const item of itemsToRetry) {
      if (this.shouldStop) break;

      if (item.attempts < this.currentSettings.retryAttempts) {
        item.attempts++;
        console.log(`Retry attempt ${item.attempts} for ${item.group.name}`);

        const groupElement = this.findGroupElement(item.group);
        if (groupElement) {
          const result = await this.attemptLeaveGroup(groupElement, item.group.name);
          if (result === true) {
            this.processStats.left++;
            this.leftGroups.push(item.group.name); // Track successfully left group
            console.log(`Successfully left group on retry: ${item.group.name}`);

            // Remove the group element from DOM for retry success too
            this.removeGroupFromDOM(groupElement, item.group.name);
          } else if (result === 'admin') {
            // Group was detected as admin-owned during retry
            console.log(`Skipping ${item.group.name} on retry - detected as admin-owned`);
            this.processStats.skipped++;
            this.skippedGroups.push(item.group.name);
          } else if (item.attempts < this.currentSettings.retryAttempts) {
            this.retryQueue.push(item); // Add back for another retry
          } else {
            this.processStats.failed++;
            this.failedGroups.push(item.group.name);
          }
        } else {
          this.processStats.failed++;
          this.failedGroups.push(item.group.name);
        }

        this.sendProgressUpdate();
        await this.smartDelay(this.currentSettings.actionDelay, 'retry', `Retrying failed group`);
      } else {
        this.processStats.failed++;
        this.failedGroups.push(item.group.name);
      }
    }
  }

  pauseProcess() {
    this.isPaused = true;
    console.log("Process paused");
  }

  resumeProcess() {
    this.isPaused = false;
    console.log("Process resumed");
  }

  stopProcess() {
    this.shouldStop = true;
    this.isPaused = false;
    console.log("Process stopped");
  }

  // New UX control methods
  continueManualStep() {
    this.waitingForUser = false;
    console.log("Continuing from manual step");
  }

  setManualMode(enabled) {
    this.manualMode = enabled;
    console.log(`Manual mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Calculate ETA based on current progress
  calculateETA() {
    if (!this.processingStartTime || this.processStats.processed === 0) {
      return null;
    }

    const elapsed = Date.now() - this.processingStartTime;
    const avgTimePerGroup = elapsed / this.processStats.processed;
    const remaining = this.processStats.total - this.processStats.processed;
    const etaMs = remaining * avgTimePerGroup;

    return {
      etaMs: etaMs,
      etaFormatted: this.formatDuration(etaMs),
      avgTimePerGroup: avgTimePerGroup,
      elapsedFormatted: this.formatDuration(elapsed)
    };
  }

  formatDuration(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
}

// Create global processor instance
const groupLeaverProcessor = new GroupLeaverProcessor();

// Global debug functions for console testing
window.debugGroupExtraction = () => {
  groupLeaverProcessor.debugGroupExtraction();
};

window.debugDOMElements = () => {
  groupLeaverProcessor.debugDOMElements();
};

// Global test function for quick selector testing
window.testGroupSelectors = () => {
  console.log('=== TESTING GROUP SELECTORS ===');
  const groupItems = document.querySelectorAll(GROUP_ITEM_SELECTOR);
  console.log(`Found ${groupItems.length} group items with selector: ${GROUP_ITEM_SELECTOR}`);

  if (groupItems.length > 0) {
    console.log('Testing first group item:');
    const firstItem = groupItems[0];
    console.log('HTML snippet:', firstItem.outerHTML.substring(0, 300) + '...');

    GROUP_NAME_SELECTORS.forEach((selector, index) => {
      const element = firstItem.querySelector(selector);
      console.log(`${index + 1}. ${selector}: ${element ? `"${element.textContent?.trim()}"` : 'NOT FOUND'}`);
    });
  }
  console.log('=== END TEST ===');
};

// Global test function for modal detection
window.testModalDetection = () => {
  console.log('=== TESTING MODAL DETECTION ===');
  const modal = groupLeaverProcessor.detectReportGroupModal();
  if (modal) {
    console.log('✅ Modal detected:', modal);
    console.log('Modal text content:', modal.textContent?.substring(0, 200) + '...');
    console.log('Modal HTML snippet:', modal.outerHTML.substring(0, 300) + '...');
  } else {
    console.log('❌ No modal detected');
  }
  console.log('=== END MODAL TEST ===');
};

// Global test function for modal dismissal
window.testModalDismissal = async () => {
  console.log('=== TESTING MODAL DISMISSAL ===');
  const modal = groupLeaverProcessor.detectReportGroupModal();
  if (modal) {
    console.log('Modal found, attempting dismissal...');
    const success = await groupLeaverProcessor.dismissReportGroupModal(modal);
    console.log(`Dismissal ${success ? 'successful' : 'failed'}`);
  } else {
    console.log('No modal found to dismiss');
  }
  console.log('=== END DISMISSAL TEST ===');
};

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  console.log("Content script received message:", request.action);

  switch (request.action) {
    case "ping":
      // Respond to ping from background script
      sendResponse({
        status: "ready",
        url: window.location.href,
        isGroupsPage: groupLeaverProcessor.isGroupsPage(window.location.href),
        groupCount: document.querySelectorAll(GROUP_ITEM_SELECTOR).length
      });
      break;
    case "scanGroups":
      // Handle async scanGroups method with tier-specific parameters
      (async () => {
        try {
          const enableAutoScroll = request.enableAutoScroll !== false;
          const scanMode = request.scanMode || 'auto';
          const groups = await groupLeaverProcessor.scanGroups(enableAutoScroll, scanMode);
          console.log(`Scanned ${groups.length} groups using ${scanMode} mode`);
          sendResponse({ groups: groups });
        } catch (error) {
          console.error("Error scanning groups:", error);
          sendResponse({ error: error.message });
        }
      })();
      return true; // Keep the message channel open for async response

    case "debugGroups":
      try {
        groupLeaverProcessor.debugGroupExtraction();
        sendResponse({ status: "Debug completed - check console" });
      } catch (error) {
        console.error("Error in debug:", error);
        sendResponse({ error: error.message });
      }
      break;

    case "startLeavingGroups":
      console.log("Starting enhanced group leaving process");
      groupLeaverProcessor.startLeavingGroups(request.groups, request.settings)
        .then(() => {
          sendResponse({ status: "Process completed" });
        })
        .catch(error => {
          console.error("Error in group leaving process:", error);
          sendResponse({ status: `Error: ${error.message}` });
        });
      return true; // Indicates async response

    case "pauseProcess":
      groupLeaverProcessor.pauseProcess();
      sendResponse({ status: "Process paused" });
      break;

    case "resumeProcess":
      groupLeaverProcessor.resumeProcess();
      sendResponse({ status: "Process resumed" });
      break;

    case "stopProcess":
      groupLeaverProcessor.stopProcess();
      sendResponse({ status: "Process stopped" });
      break;

    case "skipDelay":
      groupLeaverProcessor.skipCurrentDelay = true;
      sendResponse({ status: "Delay skipped" });
      break;

    case "continueManual":
      groupLeaverProcessor.continueManualStep();
      sendResponse({ status: "Manual step continued" });
      break;

    case "setManualMode":
      groupLeaverProcessor.setManualMode(request.enabled);
      sendResponse({ status: `Manual mode ${request.enabled ? 'enabled' : 'disabled'}` });
      break;

    case "getETA":
      const eta = groupLeaverProcessor.calculateETA();
      sendResponse({ eta: eta });
      break;

    case "clearLeftGroups":
      groupLeaverProcessor.leftGroups = [];
      groupLeaverProcessor.skippedGroups = [];
      groupLeaverProcessor.failedGroups = [];
      console.log("Cleared all group tracking arrays");
      sendResponse({ status: "Group tracking cleared" });
      break;

    case "stopAutoDiscovery":
      groupLeaverProcessor.stopAutoDiscovery = true;
      console.log("Auto-discovery stop requested");
      sendResponse({ status: "Auto-discovery stopped" });
      break;

    default:
      console.warn("Unknown action:", request.action);
      sendResponse({ error: "Unknown action" });
  }
});