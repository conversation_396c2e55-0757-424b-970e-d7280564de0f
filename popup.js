// Enhanced Facebook Group Leaver Popup Script
class GroupLeaverUI {
  constructor() {
    this.currentTab = null;
    this.isProcessing = false;
    this.isPaused = false;
    this.processStats = {
      total: 0,
      processed: 0,
      left: 0,
      skipped: 0,
      failed: 0
    };
    this.startTime = null;
    this.selectedGroups = new Set();
    this.availableGroups = [];
    this.filteredGroups = []; // Groups after applying search/filter

    // Enhanced UX controls
    this.currentDelayInfo = null;
    this.etaUpdateInterval = null;
    this.manualMode = false;
    this.backgroundPort = null;
    this.contentScriptReady = false;
    this.autoRefreshEnabled = true;
    this.lastTabState = null;

    // Search and Filter state - Three-state sorting system
    this.searchQuery = '';
    this.sortBy = null; // null (default), 'name', 'activity'
    this.sortOrder = null; // null (default), 'asc', 'desc'
    this.activityFilter = 'all'; // 'all', 'active', 'inactive'
    this.selectionFilter = 'all'; // 'all', 'selected', 'unselected'

    // Group state tracking for better refresh handling
    this.leftGroups = new Set(); // Track groups that were successfully left
    this.lastRefreshTime = 0;
    this.refreshRetryCount = 0;

    // Optimal Facebook URLs for the extension (prioritize joins page)
    this.optimalUrls = [
      'https://www.facebook.com/groups/joins/',
      'https://www.facebook.com/groups/feed/',
      'https://www.facebook.com/groups/'
    ];

    this.initializeElements();
    this.loadSettings();
    this.loadHistory();
    this.setupEventListeners();
    this.setupRealtimeConnection();
    this.updatePremiumFeatureStates();

    // Set up tab visibility detection for background processing upgrade prompts
    this.setupTabVisibilityDetection();

    // Show navigation helper by default until we confirm we're on the right page
    this.showNavigationHelper();
    this.checkCurrentPage();
  }

  initializeElements() {
    // Tab elements
    this.tabButtons = document.querySelectorAll('.tab-button');
    this.tabContents = document.querySelectorAll('.tab-content');

    // Hero section elements
    this.heroSection = document.querySelector('.hero-section');
    this.statusIcon = document.getElementById('statusIcon');
    this.statusTitle = document.getElementById('statusTitle');
    this.statusMessage = document.getElementById('statusMessage');
    this.navigateButton = document.getElementById('navigateButton');
    this.heroUpgradeButton = document.getElementById('heroUpgradeButton');

    // Groups overview elements
    this.groupsOverview = document.getElementById('groupsOverview');
    this.totalGroupsCount = document.getElementById('totalGroupsCount');
    this.selectedGroupsCount = document.getElementById('selectedGroupsCount');
    this.sessionLimitStat = document.getElementById('sessionLimitStat');
    this.sessionLimit = document.getElementById('sessionLimit');

    // Top progress indicator elements
    this.topProgressIndicator = document.getElementById('topProgressIndicator');
    this.topProgressText = document.getElementById('topProgressText');
    this.topProgressFill = document.getElementById('topProgressFill');
    this.stopDiscoveryBtn = document.getElementById('stopDiscoveryBtn');

    // Group selection elements
    this.groupSelection = document.getElementById('groupSelection');
    this.groupList = document.getElementById('groupList');

    // Search and filter elements
    this.groupSearchInput = document.getElementById('groupSearchInput');
    this.searchClearBtn = document.getElementById('searchClearBtn');
    this.sortNameBtn = document.getElementById('sortNameBtn');
    this.sortActivityBtn = document.getElementById('sortActivityBtn');
    this.sortNameIndicator = document.getElementById('sortNameIndicator');
    this.sortActivityIndicator = document.getElementById('sortActivityIndicator');
    this.activityFilterSelect = document.getElementById('activityFilter');
    this.selectionFilterSelect = document.getElementById('selectionFilter');
    this.clearFiltersBtn = document.getElementById('clearFiltersBtn');
    this.filterStatus = document.getElementById('filterStatus');
    this.filterStatusText = document.getElementById('filterStatusText');
    this.filterStatusCount = document.getElementById('filterStatusCount');

    // Premium overlay elements
    this.activityFilterOverlay = document.getElementById('activityFilterOverlay');
    this.selectionFilterOverlay = document.getElementById('selectionFilterOverlay');
    this.sortActivityOverlay = document.getElementById('sortActivityOverlay');

    // Session controls elements
    this.sessionControls = document.getElementById('sessionControls');
    this.sessionControlsCompact = document.getElementById('sessionControlsCompact');
    this.sessionBadge = document.getElementById('sessionBadge');

    // Progress elements
    this.progressSection = document.getElementById('progressSection');
    this.progressFill = document.getElementById('progressFill');
    this.progressStats = document.getElementById('progressStats');
    this.progressTime = document.getElementById('progressTime');
    this.progressPercentage = document.getElementById('progressPercentage');
    this.leftCount = document.getElementById('leftCount');
    this.skippedCount = document.getElementById('skippedCount');
    this.failedCount = document.getElementById('failedCount');
    this.currentAction = document.getElementById('currentAction');

    // Button elements
    this.startButton = document.getElementById('startButton');
    this.pauseButton = document.getElementById('pauseButton');
    this.resumeButton = document.getElementById('resumeButton');
    this.stopButton = document.getElementById('stopButton');
    this.refreshGroupsBtn = document.getElementById('refreshGroupsBtn');
    this.selectAllBtn = document.getElementById('selectAllBtn');
    this.selectNoneBtn = document.getElementById('selectNoneBtn');
    this.debugGroupsBtn = document.getElementById('debugGroupsBtn');



    // Settings elements - Auto-save structure
    this.autoScroll = document.getElementById('autoScroll');
    this.confirmBeforeStart = document.getElementById('confirmBeforeStart');
    this.skipRecentlyActive = document.getElementById('skipRecentlyActive'); // Legacy - feature removed
    this.skipAdminGroups = document.getElementById('skipAdminGroups');
    this.retryFailedGroups = document.getElementById('retryFailedGroups'); // Legacy - feature removed
    this.detailedAnalytics = document.getElementById('detailedAnalytics'); // Legacy - feature removed
    this.upgradeBtn = document.getElementById('upgradeBtn');


    // Premium feature state
    this.isPremiumUser = false; // TODO: Replace with actual premium check

    // History elements
    this.historyList = document.getElementById('historyList');
    this.clearHistoryBtn = document.getElementById('clearHistoryBtn');

    // Upgrade prompt elements
    this.selectionLimitUpgradePrompt = document.getElementById('selectionLimitUpgradePrompt');
    this.tabSwitchUpgradePrompt = document.getElementById('tabSwitchUpgradePrompt');
    this.upgradeFromLimitBtn = document.getElementById('upgradeFromLimitBtn');
    this.upgradeFromTabSwitchBtn = document.getElementById('upgradeFromTabSwitchBtn');

    // Premium feature indicators
    this.sessionLimitIndicator = document.getElementById('sessionLimitIndicator');

    // Enhanced UX elements (will be created dynamically)
    this.delayControls = null;
    this.etaDisplay = null;
  }

  setupEventListeners() {
    // Tab navigation
    this.tabButtons.forEach(button => {
      button.addEventListener('click', () => this.switchTab(button.dataset.tab));
    });

    // Main action buttons
    this.startButton.addEventListener('click', () => this.startProcess());
    this.pauseButton.addEventListener('click', () => this.pauseProcess());
    this.resumeButton.addEventListener('click', () => this.resumeProcess());
    this.stopButton.addEventListener('click', () => this.stopProcess());

    // Group management buttons
    this.refreshGroupsBtn.addEventListener('click', () => this.refreshGroups(true)); // Clear left groups on manual refresh
    this.selectAllBtn.addEventListener('click', () => this.selectAllGroups());
    this.selectNoneBtn.addEventListener('click', () => this.selectNoGroups());
    this.debugGroupsBtn.addEventListener('click', () => this.debugGroups());



    // Navigation helper button
    this.navigateButton.addEventListener('click', () => this.navigateToOptimalPage());

    // Hero upgrade button
    if (this.heroUpgradeButton) {
      this.heroUpgradeButton.addEventListener('click', () => this.showUpgradeModal());
    }

    // Auto-save event listeners for essential settings
    this.setupAutoSaveListeners();

    // Upgrade button handlers
    if (this.upgradeBtn) {
      this.upgradeBtn.addEventListener('click', () => this.showUpgradeModal());
    }

    // Premium feature click handlers
    document.querySelectorAll('.premium-disabled').forEach(element => {
      element.addEventListener('click', (e) => {
        e.preventDefault();
        this.showUpgradeModal();
      });
    });

    // History button
    this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());

    // Upgrade button event listeners
    if (this.upgradeFromGroupsBtn) {
      this.upgradeFromGroupsBtn.addEventListener('click', () => this.showUpgradeModal());
    }
    if (this.upgradeFromLimitBtn) {
      this.upgradeFromLimitBtn.addEventListener('click', () => this.showUpgradeModal());
    }
    if (this.upgradeFromTabSwitchBtn) {
      this.upgradeFromTabSwitchBtn.addEventListener('click', () => this.showUpgradeModal());
    }

    // Top progress indicator button
    if (this.stopDiscoveryBtn) {
      this.stopDiscoveryBtn.addEventListener('click', () => this.stopAutoDiscovery());
    }

    // Search and filter event listeners
    this.setupSearchAndFilterListeners();

    // Listen for messages from content script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
    });
  }

  setupSearchAndFilterListeners() {
    // Search input
    if (this.groupSearchInput) {
      this.groupSearchInput.addEventListener('input', (e) => {
        this.searchQuery = e.target.value.toLowerCase().trim();
        this.updateSearchClearButton();
        this.applyFiltersAndSort();
      });
    }

    // Search clear button
    if (this.searchClearBtn) {
      this.searchClearBtn.addEventListener('click', () => {
        this.groupSearchInput.value = '';
        this.searchQuery = '';
        this.updateSearchClearButton();
        this.applyFiltersAndSort();
      });
    }

    // Sort by name button (Free tier) - Three-state system
    if (this.sortNameBtn) {
      this.sortNameBtn.addEventListener('click', () => {
        this.cycleSortState('name');
        this.updateSortButtons();
        this.applyFiltersAndSort();
      });
    }

    // Sort by activity button (Premium tier) - Three-state system
    if (this.sortActivityBtn) {
      this.sortActivityBtn.addEventListener('click', () => {
        if (!this.isPremiumUser) {
          this.showUpgradeModal('sort-by-date');
          return;
        }
        this.cycleSortState('activity');
        this.updateSortButtons();
        this.applyFiltersAndSort();
      });
    }

    // Activity filter (Premium tier)
    if (this.activityFilterSelect) {
      this.activityFilterSelect.addEventListener('change', (e) => {
        if (!this.isPremiumUser) {
          e.target.value = 'all';
          this.showUpgradeModal('activity-filter');
          return;
        }
        this.activityFilter = e.target.value;
        this.applyFiltersAndSort();
      });
    }

    // Selection filter (Premium tier)
    if (this.selectionFilterSelect) {
      this.selectionFilterSelect.addEventListener('change', (e) => {
        if (!this.isPremiumUser) {
          e.target.value = 'all';
          this.showUpgradeModal('selection-filter');
          return;
        }
        this.selectionFilter = e.target.value;
        this.applyFiltersAndSort();
      });
    }

    // Clear filters button
    if (this.clearFiltersBtn) {
      this.clearFiltersBtn.addEventListener('click', () => {
        this.clearAllFilters();
      });
    }

    // Premium overlay click handlers
    if (this.activityFilterOverlay) {
      this.activityFilterOverlay.addEventListener('click', () => {
        this.showUpgradeModal('activity-filter');
      });
    }

    if (this.selectionFilterOverlay) {
      this.selectionFilterOverlay.addEventListener('click', () => {
        this.showUpgradeModal('selection-filter');
      });
    }

    if (this.sortActivityOverlay) {
      this.sortActivityOverlay.addEventListener('click', () => {
        this.showUpgradeModal('sort-by-date');
      });
    }
  }

  setupRealtimeConnection() {
    // Connect to background script for real-time updates
    this.backgroundPort = chrome.runtime.connect({ name: 'popup' });

    this.backgroundPort.onMessage.addListener((message) => {
      this.handleBackgroundMessage(message);
    });

    this.backgroundPort.onDisconnect.addListener(() => {
      console.log('Background connection lost');
      this.backgroundPort = null;

      // Try to reconnect after a short delay
      setTimeout(() => {
        if (!this.backgroundPort) {
          this.setupRealtimeConnection();
        }
      }, 1000);
    });

    // Request current tab state from background
    chrome.runtime.sendMessage({ action: 'getTabState' }, (response) => {
      if (response && !response.error) {
        this.handleTabStateUpdate(response);
      }
    });
  }

  handleBackgroundMessage(message) {
    switch (message.action) {
      case 'urlChanged':
        this.handleUrlChange(message);
        break;

      case 'contentScriptReady':
        this.handleContentScriptReady(message);
        break;

      case 'pageContentChanged':
        this.handlePageContentChanged(message);
        break;

      case 'tabActivated':
        this.handleTabActivated(message);
        break;

      case 'pageReadinessDetected':
        this.handlePageReadinessDetected(message);
        break;

      default:
        // Forward other messages to the regular message handler
        this.handleMessage(message, null, null);
    }
  }

  handleUrlChange(message) {
    console.log('URL changed:', message.url);

    // Update current tab info
    if (this.currentTab && message.tabId === this.currentTab.id) {
      this.currentTab.url = message.url;
      this.contentScriptReady = false;

      // Clear current groups since we're on a new page
      this.selectedGroups.clear();
      this.availableGroups = [];
      this.groupPreview.classList.add('hidden');

      // Update status based on new URL
      const pageStatus = this.analyzePageCompatibility(message.url);
      if (pageStatus.isOptimal || pageStatus.isCompatible) {
        this.updateStatus('🔄', 'Page Changed', 'Detecting content on new page...', 'info');
        this.hideNavigationHelper();
      } else {
        this.showNavigationHelper();
      }
    }
  }

  handleContentScriptReady(message) {
    console.log('Content script ready:', message);

    if (this.currentTab && message.tabId === this.currentTab.id) {
      this.contentScriptReady = true;

      if (message.isGroupsPage) {
        // Hide navigation helper immediately since we're on the right page
        this.hideNavigationHelper();

        // Always auto-scan when content script is ready on groups page
        this.updateStatus('✅', 'Auto-Scanning', 'Content script ready - scanning groups automatically...', 'success');
        this.startButton.disabled = false;

        // Trigger immediate auto-scan with minimal delay
        setTimeout(() => {
          this.refreshGroups();
        }, 200);
      }
    }
  }

  handlePageContentChanged(message) {
    console.log('Page content changed:', message.data);

    if (this.currentTab && message.tabId === this.currentTab.id && this.autoRefreshEnabled) {
      // Debounce content change updates
      clearTimeout(this.contentChangeTimeout);
      this.contentChangeTimeout = setTimeout(() => {
        if (message.data.groupCount > 0 && this.availableGroups.length === 0) {
          // New groups detected, auto-refresh
          this.refreshGroups();
        }
      }, 1000);
    }
  }

  handleTabActivated(message) {
    console.log('Tab activated:', message.tabId);

    // Re-check current page when tab is activated
    setTimeout(() => {
      this.checkCurrentPage();
    }, 100);
  }

  handlePageReadinessDetected(message) {
    console.log('Page readiness detected:', message.data);

    if (this.currentTab && message.tabId === this.currentTab.id) {
      const { groupCount, isGroupsPage } = message.data;

      if (isGroupsPage) {
        // Hide navigation helper since we're on the right page
        this.hideNavigationHelper();

        if (groupCount > 0) {
          // Groups detected - trigger immediate auto-scan
          this.updateStatus('✅', 'Auto-Scanning', `Detected ${groupCount} groups - scanning automatically...`, 'success');
          this.triggerImmediateAutoScan();
        } else {
          // On groups page but no groups found yet
          this.updateStatus('🔄', 'Monitoring', 'Monitoring page for groups...', 'info');
        }
      }
    }
  }

  handleTabStateUpdate(state) {
    this.lastTabState = state;
    this.currentTab = state.tab;
    this.contentScriptReady = state.contentScriptReady;

    if (state.isGroupsPage && state.contentScriptReady) {
      // Hide navigation helper immediately
      this.hideNavigationHelper();
      this.startButton.disabled = false;

      // Always auto-scan when we detect we're on the right page with content script ready
      this.updateStatus('✅', 'Auto-Scanning', 'Detected Facebook Groups page - scanning automatically...', 'success');

      // Trigger immediate auto-scan
      setTimeout(() => {
        this.refreshGroups();
      }, 100);
    } else if (state.isGroupsPage && !state.contentScriptReady) {
      // On groups page but content script not ready yet
      this.updateStatus('🔄', 'Loading', 'Loading content script on Facebook Groups page...', 'info');
      this.hideNavigationHelper();

      // Test connection with auto-scan capability
      setTimeout(() => {
        this.testContentScriptConnectionWithAutoScan();
      }, 500);
    } else if (!state.isGroupsPage) {
      this.showNavigationHelper();
    }
  }

  // Three-state sorting system: Default → Ascending → Descending → Default
  cycleSortState(sortType) {
    if (this.sortBy === sortType) {
      // Currently sorting by this type, cycle through states
      if (this.sortOrder === 'asc') {
        this.sortOrder = 'desc';
      } else if (this.sortOrder === 'desc') {
        // Reset to default (no sort)
        this.sortBy = null;
        this.sortOrder = null;
      }
    } else {
      // Not currently sorting by this type, start with ascending
      this.sortBy = sortType;
      this.sortOrder = sortType === 'activity' ? 'desc' : 'asc'; // Activity defaults to most recent first
    }
  }

  switchTab(tabName) {
    // Update tab buttons
    this.tabButtons.forEach(btn => {
      btn.classList.toggle('active', btn.dataset.tab === tabName);
    });

    // Update tab content
    this.tabContents.forEach(content => {
      content.classList.toggle('active', content.id === `${tabName}-tab`);
    });
  }

  async checkCurrentPage() {
    try {
      // Request current state from background script
      chrome.runtime.sendMessage({ action: 'getTabState' }, (response) => {
        if (chrome.runtime.lastError) {
          console.error('Failed to get tab state:', chrome.runtime.lastError);
          this.fallbackPageCheck();
          return;
        }

        if (response && !response.error) {
          this.handleTabStateUpdate(response);

          // Auto-detect and immediately scan if on correct page
          if (response.isGroupsPage && response.contentScriptReady) {
            this.triggerImmediateAutoScan();
          }
        } else {
          this.fallbackPageCheck();
        }
      });
    } catch (error) {
      console.error('Page detection error:', error);
      this.fallbackPageCheck();
    }
  }

  async fallbackPageCheck() {
    // Fallback to direct tab query if background communication fails
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;

      if (tab && tab.url) {
        const pageStatus = this.analyzePageCompatibility(tab.url);

        if (pageStatus.isOptimal || pageStatus.isCompatible) {
          this.updateStatus('🔄', 'Checking Content', 'Checking if content script is ready...', 'info');
          this.startButton.disabled = false;
          this.hideNavigationHelper();

          // Try to communicate with content script and auto-scan if ready
          this.testContentScriptConnectionWithAutoScan();
        } else {
          this.showNavigationHelper();
        }
      }
    } catch (error) {
      this.updateStatus('❌', 'Error', 'Failed to detect current page', 'error');
      console.error('Fallback page detection error:', error);
    }
  }

  // New method for immediate auto-scanning when already on correct page
  triggerImmediateAutoScan() {
    console.log('Triggering immediate auto-scan - user is already on correct page');

    // Hide navigation helper immediately
    this.hideNavigationHelper();

    // Update status to show we're auto-scanning with animation
    this.updateStatus('🔄', 'Auto-Scanning', 'Automatically scanning groups on this page...', 'info');

    // Add auto-scanning animation
    const heroCard = this.heroSection.querySelector('.hero-status-card');
    if (heroCard) {
      heroCard.classList.add('auto-scanning');
    }

    // Trigger immediate group refresh with a short delay for UI feedback
    setTimeout(() => {
      this.refreshGroups(true); // Clear left groups for fresh auto-scan
    }, 300);
  }

  // Enhanced content script connection test with auto-scan capability
  testContentScriptConnectionWithAutoScan() {
    if (!this.currentTab) return;

    // Initialize retry tracking if not exists
    if (!this.connectionRetries) {
      this.connectionRetries = 0;
    }

    const maxRetries = 8;
    const retryDelay = Math.min(1000 + (this.connectionRetries * 500), 3000);

    chrome.tabs.sendMessage(this.currentTab.id, { action: 'ping' }, (response) => {
      if (chrome.runtime.lastError) {
        this.connectionRetries++;
        console.log(`Content script connection attempt ${this.connectionRetries}/${maxRetries} failed:`, chrome.runtime.lastError.message);

        if (this.connectionRetries < maxRetries) {
          this.updateStatus('🔄', 'Connecting', `Establishing connection (${this.connectionRetries}/${maxRetries})...`, 'info');
          this.contentScriptReady = false;

          // Try to ensure content script is injected
          if (this.currentTab.url.includes('facebook.com/groups')) {
            this.ensureContentScriptInjected();
          }

          // Retry connection after a delay
          setTimeout(() => {
            this.testContentScriptConnectionWithAutoScan();
          }, retryDelay);
        } else {
          // Max retries reached
          this.updateStatus('❌', 'Connection Failed', 'Could not connect to page. Try refreshing the page (F5).', 'error');
          this.contentScriptReady = false;
          this.connectionRetries = 0;
        }
      } else if (response && response.status === 'ready') {
        console.log('Content script connection successful:', response);
        this.contentScriptReady = true;
        this.connectionRetries = 0; // Reset retry counter on success

        // Immediately trigger auto-scan if we have groups available
        if (response.groupCount && response.groupCount > 0) {
          this.updateStatus('✅', 'Auto-Scanning', 'Groups detected - scanning automatically...', 'success');
          this.triggerImmediateAutoScan();
        } else {
          this.updateStatus('✅', 'Ready', 'Ready to scan your Facebook groups', 'success');

          // Still auto-refresh if enabled, even if no groups initially detected
          if (this.autoRefreshEnabled) {
            setTimeout(() => {
              this.refreshGroups();
            }, 500);
          }
        }
      }
    });
  }

  async ensureContentScriptInjected() {
    if (!this.currentTab) return;

    try {
      console.log('Attempting to inject content script...');
      await chrome.scripting.executeScript({
        target: { tabId: this.currentTab.id },
        files: ['content.js']
      });
      console.log('Content script injection attempted');
    } catch (error) {
      console.error('Failed to inject content script:', error);
    }
  }

  showNavigationHelper() {
    // Don't override hero section if groups are already loaded
    if (this.availableGroups && this.availableGroups.length > 0) {
      return;
    }

    // Update hero section to show navigation required state
    this.updateStatus('🧭', 'Navigation Required', 'Click below to automatically navigate to Facebook Groups', 'warning');
    this.startButton.disabled = true;

    // Show navigation button, hide upgrade button
    this.navigateButton.classList.remove('hidden');
    this.heroUpgradeButton.classList.add('hidden');

    // Hide other sections and show only hero
    this.groupsOverview.classList.add('hidden');
    this.groupSelection.classList.add('hidden');
    this.sessionControls.classList.add('hidden');
    this.sessionControlsCompact.classList.add('hidden');
    this.progressSection.classList.add('hidden');

    // Update hero section styling for navigation state
    const heroCard = this.heroSection.querySelector('.hero-status-card');
    heroCard.style.borderLeftColor = '#F59E0B';
    heroCard.style.background = '#FFFBEB';
  }

  hideNavigationHelper() {
    // This method is called when we want to hide the navigation helper state
    // The actual hiding is handled by updating the status and enabling the start button
    this.startButton.disabled = false;
  }

  testContentScriptConnection() {
    if (!this.currentTab) return;

    chrome.tabs.sendMessage(this.currentTab.id, { action: 'ping' }, (response) => {
      if (chrome.runtime.lastError) {
        this.updateStatus('⚠️', 'Processing', 'Content script ready on this page.', 'warning');
        this.contentScriptReady = false;
      } else if (response && response.status === 'ready') {
        this.contentScriptReady = true;
        this.updateStatus('✅', 'Ready', 'Ready to scan your Facebook groups', 'success');

        if (this.autoRefreshEnabled) {
          this.refreshGroups();
        }
      }
    });
  }

  analyzePageCompatibility(url) {
    // Check for optimal pages (best performance)
    if (url.includes('facebook.com/groups/joins/')) {
      return {
        isOptimal: true,
        isCompatible: true,
        message: 'Ready to scan groups'
      };
    }

    if (url.includes('facebook.com/groups/feed/')) {
      return {
        isOptimal: true,
        isCompatible: true,
        message: 'Ready to scan groups'
      };
    }

    // Check for compatible pages (will work but not optimal)
    if (url.includes('facebook.com/groups/') && !url.includes('/groups/create/')) {
      return {
        isOptimal: false,
        isCompatible: true,
        message: 'Facebook Groups page detected'
      };
    }

    // Check for Facebook but not groups
    if (url.includes('facebook.com')) {
      return {
        isOptimal: false,
        isCompatible: false,
        message: 'Navigate to Facebook Groups'
      };
    }

    // Not on Facebook at all
    return {
      isOptimal: false,
      isCompatible: false,
      message: 'Navigate to Facebook Groups'
    };
  }

  async navigateToOptimalPage() {
    if (!this.currentTab) return;

    try {
      // Use the optimal Facebook Groups joins page for better group detection
      const optimalUrl = 'https://www.facebook.com/groups/joins/';

      // Update button state to show loading
      this.navigateButton.disabled = true;
      this.navigateButton.querySelector('.btn-text').textContent = 'Opening...';
      this.navigateButton.querySelector('.btn-icon').textContent = '⏳';

      await chrome.tabs.update(this.currentTab.id, { url: optimalUrl });

      this.updateStatus('🚀', 'Navigating', 'Opening Facebook Groups page...', 'info');

      // Set up a more robust page detection with multiple checks
      let checkAttempts = 0;
      const maxAttempts = 10;

      const checkPageReady = () => {
        checkAttempts++;

        chrome.tabs.get(this.currentTab.id, (tab) => {
          if (chrome.runtime.lastError) {
            console.error('Tab check error:', chrome.runtime.lastError);
            this.resetNavigationButton();
            return;
          }

          if (tab && tab.url && tab.url.includes('facebook.com/groups')) {
            // Page has navigated, now check for content script readiness with auto-scan
            this.testContentScriptConnectionWithAutoScan();

            // Auto-enable group scanning after successful navigation
            setTimeout(() => {
              if (this.contentScriptReady) {
                this.updateStatus('✅', 'Auto-Scanning', 'Successfully navigated - scanning groups automatically...', 'success');
                this.triggerImmediateAutoScan();
              }
            }, 1000);

            this.resetNavigationButton();
          } else if (checkAttempts < maxAttempts) {
            // Keep checking
            setTimeout(checkPageReady, 500);
          } else {
            // Max attempts reached
            this.updateStatus('⚠️', 'Navigation Timeout', 'Navigation took longer than expected. Please try refreshing.', 'warning');
            this.resetNavigationButton();
          }
        });
      };

      // Start checking after a short delay
      setTimeout(checkPageReady, 1000);

    } catch (error) {
      console.error('Navigation error:', error);
      this.updateStatus('❌', 'Navigation Failed', 'Could not navigate to Facebook Groups page', 'error');
      this.resetNavigationButton();
    }
  }

  resetNavigationButton() {
    this.navigateButton.disabled = false;
    this.navigateButton.querySelector('.btn-text').textContent = 'Open Groups Page';
    this.navigateButton.querySelector('.btn-icon').textContent = '🚀';
  }

  updateStatus(icon, title, message, type = 'info') {
    this.statusIcon.textContent = icon;
    this.statusTitle.textContent = title;
    this.statusMessage.textContent = message;

    // Update hero card styling based on type
    const heroCard = this.heroSection.querySelector('.hero-status-card');
    if (heroCard) {
      // Reset to default styling
      heroCard.style.borderLeftColor = '#FF90E8';
      heroCard.style.background = '#F4F4F0';

      // Apply type-specific styling (button switching is handled by specific methods)
      switch (type) {
        case 'success':
          heroCard.style.borderLeftColor = '#23A094';
          heroCard.style.background = '#F0FDF4';
          break;
        case 'warning':
          heroCard.style.borderLeftColor = '#F59E0B';
          heroCard.style.background = '#FFFBEB';
          break;
        case 'error':
          heroCard.style.borderLeftColor = '#DC2626';
          heroCard.style.background = '#FEF2F2';
          // Don't override button visibility here - let specific methods handle it
          break;
        default:
          // Keep default styling for 'info'
          // Don't override button visibility here - let specific methods handle it
          break;
      }
    }

    // Add connection status indicator
    this.updateConnectionStatus();
  }

  updateConnectionStatus() {
    // Add a small indicator showing real-time connection status
    let connectionIndicator = document.querySelector('.connection-status');
    if (!connectionIndicator) {
      connectionIndicator = document.createElement('div');
      connectionIndicator.className = 'connection-status';
      connectionIndicator.title = 'Real-time connection status';
      const heroCard = this.heroSection.querySelector('.hero-status-card');
      if (heroCard) {
        heroCard.appendChild(connectionIndicator);
      }
    }

    const isConnected = this.backgroundPort && this.contentScriptReady;
    connectionIndicator.className = `connection-status ${isConnected ? 'connected' : 'disconnected'}`;
    connectionIndicator.textContent = isConnected ? '🟢' : '🟡';
    connectionIndicator.title = isConnected ?
      'Real-time connection active' :
      'Establishing connection...';
  }

  async refreshGroups(clearLeftGroups = false) {
    if (!this.currentTab || !this.currentTab.url.includes('facebook.com/groups')) {
      return;
    }

    // Clear selected groups when refreshing
    this.selectedGroups.clear();

    // Optionally clear left groups tracking (for fresh scans)
    if (clearLeftGroups) {
      this.leftGroups.clear();
      // Also clear the content script's left groups tracking
      chrome.tabs.sendMessage(this.currentTab.id, { action: "clearLeftGroups" });
    }

    // Determine scan mode based on user tier
    const settings = this.getSettings();
    const scanMode = this.isPremiumUser ? 'auto' : 'manual';
    const enableAutoScroll = this.isPremiumUser && settings.autoScroll;

    // Update status with tier-specific messaging
    if (this.isPremiumUser) {
      this.updateStatus('🔄', 'Premium Scanning', 'Auto-discovering all your Facebook groups...', 'info');
      // Only show top progress indicator for premium auto-discovery
      this.showTopProgressIndicator('Auto-discovering all your Facebook groups...');
    } else {
      this.updateStatus('🔄', 'Scanning', 'Scanning visible groups...', 'info');
      // Don't show top progress indicator for manual scanning
    }

    try {
      // Send message to content script to get group information
      chrome.tabs.sendMessage(this.currentTab.id, {
        action: "scanGroups",
        enableAutoScroll: enableAutoScroll,
        scanMode: scanMode
      }, (response) => {
        if (chrome.runtime.lastError) {
          this.updateStatus('❌', 'Scan Failed', 'Could not scan groups. Please refresh the page and try again.', 'error');
          return;
        }

        if (response && response.groups) {
          this.displayGroups(response.groups);
          if (response.groups.length > 0) {
            const statusMessage = this.isPremiumUser
              ? `Found ${response.groups.length} groups - select which ones to leave`
              : `Found ${response.groups.length} visible groups - scroll down to discover more or upgrade to Premium`;
            this.updateStatus('✅', 'Groups Loaded', statusMessage, 'success');
          } else {
            const noGroupsMessage = this.isPremiumUser
              ? 'No groups found on this page. Try navigating to a different groups page.'
              : 'No visible groups found. Try scrolling down or upgrade to Premium for auto-discovery.';
            this.updateStatus('ℹ️', 'No Groups', noGroupsMessage, 'info');
          }
        } else {
          this.updateStatus('⚠️', 'No Groups', 'No groups found on this page', 'warning');
        }
      });
    } catch (error) {
      this.updateStatus('❌', 'Error', 'Failed to scan groups', 'error');
      console.error('Group scan error:', error);
    }
  }

  displayGroups(groups) {
    this.availableGroups = groups;

    // Hide the top progress indicator since scanning is complete
    this.hideTopProgressIndicator();

    if (groups.length === 0) {
      this.groupsOverview.classList.add('hidden');
      this.groupSelection.classList.add('hidden');
      this.sessionControls.classList.add('hidden');
      this.sessionControlsCompact.classList.add('hidden');
      return;
    }

    // Hide session limit stat for premium users
    if (this.isPremiumUser) {
      this.sessionLimitStat.classList.add('hidden');
    } else {
      this.sessionLimitStat.classList.remove('hidden');
    }

    // Remove auto-scanning animation since we're done
    const heroCard = this.heroSection.querySelector('.hero-status-card');
    heroCard.classList.remove('auto-scanning');

    // Update hero section based on user tier and selection state
    // This will handle the proper button visibility and messaging
    this.updateHeroSectionForLimitState(false);

    // Show and update groups overview section
    this.groupsOverview.classList.remove('hidden');
    this.totalGroupsCount.textContent = groups.length;
    this.selectedGroupsCount.textContent = '0';

    // Show group selection section
    this.groupSelection.classList.remove('hidden');
    this.groupList.innerHTML = '';

    groups.forEach((group, index) => {
      const groupItem = document.createElement('div');
      groupItem.className = 'group-item';
      const isSelected = this.selectedGroups.has(index);
      const wasLeft = this.leftGroups.has(group.name);

      groupItem.innerHTML = `
        <input type="checkbox" class="group-checkbox" id="group-${index}" ${isSelected ? 'checked' : ''}>
        <span class="group-checkmark"></span>
        <label for="group-${index}" class="group-name">${group.name}</label>
        ${group.isAdmin ? '<span class="group-status admin">Admin</span>' : ''}
        ${group.isActive ? '<span class="group-status active">Active</span>' : ''}
        ${wasLeft ? '<span class="group-status left">Left</span>' : ''}
      `;

      // Add selected class if already selected
      if (isSelected) {
        groupItem.classList.add('selected');
      }

      // Add left class if this group was successfully left
      if (wasLeft) {
        groupItem.classList.add('left-group');
        groupItem.title = 'This group was successfully left but may still appear until Facebook updates the page';
      }

      const checkbox = groupItem.querySelector('.group-checkbox');

      // Make entire group item clickable for better UX
      const toggleCheckbox = (event) => {
        // Prevent event bubbling if clicking on status badges
        if (event.target.classList.contains('group-status')) {
          event.stopPropagation();
          return;
        }

        // Prevent default behavior to avoid any checkbox interference
        event.preventDefault();
        event.stopPropagation();

        // Check if the group item is disabled (freemium limit reached)
        if (groupItem.classList.contains('disabled')) {
          return;
        }

        // Handle selection logic directly to avoid race conditions
        const isCurrentlySelected = this.selectedGroups.has(index);

        if (isCurrentlySelected) {
          // Always allow deselection
          this.selectedGroups.delete(index);
          checkbox.checked = false;
          groupItem.classList.remove('selected');
        } else {
          // Check freemium limit before selection
          if (!this.isPremiumUser && this.selectedGroups.size >= 3) {
            return; // Prevent selection beyond limit
          }
          this.selectedGroups.add(index);
          checkbox.checked = true;
          groupItem.classList.add('selected');
        }

        this.updateUI();
      };

      // Add click handler to entire group item
      groupItem.addEventListener('click', toggleCheckbox);

      // Remove the separate checkbox change handler to prevent conflicts
      // The row click handler now manages all selection logic

      this.groupList.appendChild(groupItem);
    });

    // Show compact session controls section
    this.sessionControlsCompact.classList.remove('hidden');

    // Apply smart defaults based on group count
    this.applySmartDefaults(groups.length);

    // Initialize search and filter functionality
    this.initializeSearchAndFilter();

    this.updateUI();
  }

  // Initialize search and filter functionality
  initializeSearchAndFilter() {
    // Reset filters when new groups are loaded
    this.clearAllFilters();

    // Apply initial filters and sort
    this.applyFiltersAndSort();

    // Update premium feature states
    this.updatePremiumFeatureStates();
  }

  // Apply search, filter, and sort to the group list
  applyFiltersAndSort() {
    if (!this.availableGroups || this.availableGroups.length === 0) {
      this.filteredGroups = [];
      this.updateFilterStatus();
      return;
    }

    let filtered = [...this.availableGroups];

    // Apply search filter
    if (this.searchQuery) {
      filtered = filtered.filter(group =>
        group.name.toLowerCase().includes(this.searchQuery)
      );
    }

    // Apply activity filter (Premium feature)
    if (this.isPremiumUser && this.activityFilter !== 'all') {
      filtered = filtered.filter(group => {
        if (this.activityFilter === 'active') {
          return group.isActive;
        } else if (this.activityFilter === 'inactive') {
          return !group.isActive;
        }
        return true;
      });
    }

    // Apply selection filter (Premium feature)
    if (this.isPremiumUser && this.selectionFilter !== 'all') {
      filtered = filtered.filter((group, index) => {
        const originalIndex = this.availableGroups.indexOf(group);
        const isSelected = this.selectedGroups.has(originalIndex);

        if (this.selectionFilter === 'selected') {
          return isSelected;
        } else if (this.selectionFilter === 'unselected') {
          return !isSelected;
        }
        return true;
      });
    }

    // Apply sorting (only if sortBy is not null)
    if (this.sortBy && this.sortOrder) {
      filtered.sort((a, b) => {
        if (this.sortBy === 'name') {
          const comparison = a.name.localeCompare(b.name);
          return this.sortOrder === 'asc' ? comparison : -comparison;
        } else if (this.sortBy === 'activity' && this.isPremiumUser) {
          // Sort by activity (most recent first by default)
          const aDays = a.activityInfo ? a.activityInfo.daysAgo : 999;
          const bDays = b.activityInfo ? b.activityInfo.daysAgo : 999;
          const comparison = aDays - bDays;
          return this.sortOrder === 'desc' ? comparison : -comparison;
        }
        return 0;
      });
    }

    this.filteredGroups = filtered;
    this.renderFilteredGroups();
    this.updateFilterStatus();
  }

  // Render the filtered and sorted groups
  renderFilteredGroups() {
    this.groupList.innerHTML = '';

    if (this.filteredGroups.length === 0) {
      const emptyMessage = document.createElement('div');
      emptyMessage.className = 'empty-groups-message';
      emptyMessage.innerHTML = `
        <div class="empty-icon">🔍</div>
        <div class="empty-title">No groups match your filters</div>
        <div class="empty-description">Try adjusting your search or filter criteria</div>
      `;
      this.groupList.appendChild(emptyMessage);
      return;
    }

    this.filteredGroups.forEach((group) => {
      // Find the original index in availableGroups
      const originalIndex = this.availableGroups.indexOf(group);

      const groupItem = document.createElement('div');
      groupItem.className = 'group-item';
      const isSelected = this.selectedGroups.has(originalIndex);
      const wasLeft = this.leftGroups.has(group.name);

      // Add activity info to the group display for premium users
      let activityDisplay = '';
      if (this.isPremiumUser && group.activityInfo && group.activityInfo.hasActivityInfo) {
        activityDisplay = `<span class="group-activity-info" title="Last visited: ${group.activityInfo.lastVisited}">${group.activityInfo.lastVisited}</span>`;
      }

      groupItem.innerHTML = `
        <input type="checkbox" class="group-checkbox" id="group-${originalIndex}" ${isSelected ? 'checked' : ''}>
        <span class="group-checkmark"></span>
        <label for="group-${originalIndex}" class="group-name">${group.name}</label>
        ${group.isAdmin ? '<span class="group-status admin">Admin</span>' : ''}
        ${group.isActive ? '<span class="group-status active">Active</span>' : ''}
        ${wasLeft ? '<span class="group-status left">Left</span>' : ''}
        ${activityDisplay}
      `;

      // Add selected class if already selected
      if (isSelected) {
        groupItem.classList.add('selected');
      }

      // Add left class if this group was successfully left
      if (wasLeft) {
        groupItem.classList.add('left-group');
        groupItem.title = 'This group was successfully left but may still appear until Facebook updates the page';
      }

      const checkbox = groupItem.querySelector('.group-checkbox');

      // Make entire group item clickable for better UX
      const toggleCheckbox = (event) => {
        // Prevent event bubbling if clicking on status badges
        if (event.target.classList.contains('group-status') || event.target.classList.contains('group-activity-info')) {
          event.stopPropagation();
          return;
        }

        // Prevent default behavior to avoid any checkbox interference
        event.preventDefault();
        event.stopPropagation();

        // Check if the group item is disabled (freemium limit reached)
        if (groupItem.classList.contains('disabled')) {
          return;
        }

        // Handle selection logic directly to avoid race conditions
        const isCurrentlySelected = this.selectedGroups.has(originalIndex);

        if (isCurrentlySelected) {
          // Always allow deselection
          this.selectedGroups.delete(originalIndex);
          checkbox.checked = false;
          groupItem.classList.remove('selected');
        } else {
          // Check freemium limit before selection
          if (!this.isPremiumUser && this.selectedGroups.size >= 3) {
            return; // Prevent selection beyond limit
          }
          this.selectedGroups.add(originalIndex);
          checkbox.checked = true;
          groupItem.classList.add('selected');
        }

        this.updateUI();
        // Re-apply filters to update selection filter if active
        if (this.selectionFilter !== 'all') {
          this.applyFiltersAndSort();
        }
      };

      // Add click handler to entire group item
      groupItem.addEventListener('click', toggleCheckbox);

      this.groupList.appendChild(groupItem);
    });
  }

  // Update search clear button visibility
  updateSearchClearButton() {
    if (this.searchClearBtn) {
      if (this.searchQuery) {
        this.searchClearBtn.classList.remove('hidden');
      } else {
        this.searchClearBtn.classList.add('hidden');
      }
    }
  }

  // Update sort button states with three-state system
  updateSortButtons() {
    // Update Name Sort Button
    if (this.sortNameBtn && this.sortNameIndicator) {
      if (this.sortBy === 'name') {
        this.sortNameBtn.classList.add('active');
        if (this.sortOrder === 'asc') {
          this.sortNameIndicator.textContent = '↑';
          this.sortNameBtn.title = 'Sort by name (A-Z) - Click for Z-A';
        } else if (this.sortOrder === 'desc') {
          this.sortNameIndicator.textContent = '↓';
          this.sortNameBtn.title = 'Sort by name (Z-A) - Click to clear sort';
        }
      } else {
        this.sortNameBtn.classList.remove('active');
        this.sortNameIndicator.textContent = '';
        this.sortNameBtn.title = 'Sort by name - Click for A-Z';
      }
    }

    // Update Activity Sort Button
    if (this.sortActivityBtn && this.sortActivityIndicator) {
      const isPremium = this.isPremiumUser;
      const baseTitle = isPremium ? 'Sort by Date' : 'Sort by Date (Premium Feature - $9 one-time)';

      if (this.sortBy === 'activity' && isPremium) {
        this.sortActivityBtn.classList.add('active');
        if (this.sortOrder === 'desc') {
          this.sortActivityIndicator.textContent = '↓';
          this.sortActivityBtn.title = `${baseTitle} (Most Recent) - Click for Oldest`;
        } else if (this.sortOrder === 'asc') {
          this.sortActivityIndicator.textContent = '↑';
          this.sortActivityBtn.title = `${baseTitle} (Oldest) - Click to clear sort`;
        }
      } else {
        this.sortActivityBtn.classList.remove('active');
        this.sortActivityIndicator.textContent = '';
        if (isPremium) {
          this.sortActivityBtn.title = `${baseTitle} - Click for Most Recent`;
        } else {
          this.sortActivityBtn.title = `${baseTitle} - Click to upgrade and unlock this feature`;
        }
      }
    }
  }

  // Update filter status display
  updateFilterStatus() {
    if (!this.filterStatus || !this.filterStatusText || !this.filterStatusCount) return;

    const totalGroups = this.availableGroups.length;
    const filteredCount = this.filteredGroups.length;
    const hasActiveFilters = this.searchQuery ||
                            (this.isPremiumUser && this.activityFilter !== 'all') ||
                            (this.isPremiumUser && this.selectionFilter !== 'all') ||
                            (this.sortBy !== null && this.sortOrder !== null);

    if (hasActiveFilters || filteredCount !== totalGroups) {
      this.filterStatus.classList.remove('hidden');

      let statusText = 'Showing';
      if (this.searchQuery) {
        statusText += ` search results for "${this.searchQuery}"`;
      }
      if (this.isPremiumUser && this.activityFilter !== 'all') {
        statusText += ` ${this.activityFilter} groups`;
      }
      if (this.isPremiumUser && this.selectionFilter !== 'all') {
        statusText += ` ${this.selectionFilter} groups`;
      }
      if (statusText === 'Showing') {
        statusText = 'Showing filtered results';
      }

      this.filterStatusText.textContent = statusText;
      this.filterStatusCount.textContent = `(${filteredCount} of ${totalGroups} groups)`;
    } else {
      this.filterStatus.classList.add('hidden');
    }
  }

  // Clear all filters and reset to defaults
  clearAllFilters() {
    this.searchQuery = '';
    this.sortBy = null;
    this.sortOrder = null;
    this.activityFilter = 'all';
    this.selectionFilter = 'all';

    // Update UI elements
    if (this.groupSearchInput) {
      this.groupSearchInput.value = '';
    }
    if (this.activityFilterSelect) {
      this.activityFilterSelect.value = 'all';
    }
    if (this.selectionFilterSelect) {
      this.selectionFilterSelect.value = 'all';
    }

    this.updateSearchClearButton();
    this.updateSortButtons();
    this.applyFiltersAndSort();
  }

  applySmartDefaults(groupCount) {
    // Auto-enable safety features for any batch processing
    this.skipAdminGroups.checked = true;
    this.confirmBeforeStart.checked = true;

    // Enable premium features if user has premium
    if (this.isPremiumUser) {
      // Only set features that still exist
      if (this.autoScroll) this.autoScroll.checked = true;
      // Legacy features (removed from UI but kept for compatibility):
      // - skipRecentlyActive, retryFailedGroups, detailedAnalytics
    }

    console.log(`Applied smart defaults for ${groupCount} groups`);
  }

  selectAllGroups() {
    this.selectedGroups.clear();
    const maxGroups = this.isPremiumUser ? 999 : 3;
    let selectedCount = 0;

    // Use filtered groups if available, otherwise use all available groups
    const groupsToSelect = this.filteredGroups.length > 0 ? this.filteredGroups : this.availableGroups;

    groupsToSelect.forEach((group) => {
      // Stop if we've reached the limit
      if (selectedCount >= maxGroups) return;

      // Find the original index in availableGroups
      const originalIndex = this.availableGroups.indexOf(group);
      if (originalIndex === -1) return;

      // Apply safety filters
      const settings = this.getSettings();
      if (settings.skipAdminGroups && group.isAdmin) return;
      // Note: skipRecentlyActive feature removed from UI

      this.selectedGroups.add(originalIndex);
      selectedCount++;
    });

    // Re-render filtered groups to update selection state
    this.renderFilteredGroups();
    this.updateUI();
  }

  selectNoGroups() {
    this.selectedGroups.clear();
    // Re-render filtered groups to update selection state
    this.renderFilteredGroups();
    this.updateUI();
  }

  updateGroupCheckboxes() {
    const checkboxes = this.groupList.querySelectorAll('.group-checkbox');
    const groupItems = this.groupList.querySelectorAll('.group-item');
    checkboxes.forEach((checkbox, index) => {
      const isSelected = this.selectedGroups.has(index);
      checkbox.checked = isSelected;

      // Update selected class for visual feedback
      if (isSelected) {
        groupItems[index].classList.add('selected');
      } else {
        groupItems[index].classList.remove('selected');
      }
    });
  }

  updateUI() {
    this.updateStartButton();
    this.updateSelectedCount();
    this.updateSessionBadge();
    this.updateGroupSelectionLimits();
    this.updateSelectAllButton();
  }

  updateStartButton() {
    const selectedCount = this.selectedGroups.size;
    this.startButton.disabled = selectedCount === 0 || this.isProcessing;

    if (selectedCount > 0) {
      this.startButton.querySelector('.btn-text').textContent = `Start Leaving Groups (${selectedCount})`;
    } else {
      this.startButton.querySelector('.btn-text').textContent = 'Start Leaving Groups';
    }
  }

  updateSelectedCount() {
    if (this.selectedGroupsCount) {
      this.selectedGroupsCount.textContent = this.selectedGroups.size;
    }
  }

  updateSessionBadge() {
    if (!this.sessionBadge) return;

    const selectedCount = this.selectedGroups.size;
    const settings = this.getSettings();
    const maxGroups = settings.batchSize;

    if (selectedCount === 0) {
      this.sessionBadge.textContent = 'Ready to Start';
      this.sessionBadge.style.background = '#6F6F6F';
    } else if (selectedCount > maxGroups) {
      this.sessionBadge.textContent = 'Limit Exceeded';
      this.sessionBadge.style.background = '#DC2626';
    } else {
      this.sessionBadge.textContent = `${selectedCount} Selected`;
      this.sessionBadge.style.background = '#23A094';
    }
  }

  updateGroupSelectionLimits() {
    // Only apply limits for freemium users
    if (this.isPremiumUser) {
      this.hideSelectionLimitUpgradePrompt();
      this.updateHeroSectionForLimitState(false);
      return;
    }

    const selectedCount = this.selectedGroups.size;
    const maxGroups = 3; // Freemium limit
    const limitReached = selectedCount >= maxGroups;

    // Hide the bottom selection limit prompt since we're using the hero section instead
    this.hideSelectionLimitUpgradePrompt();

    // Update hero section to show upgrade button when limit is reached
    this.updateHeroSectionForLimitState(limitReached);

    // Disable/enable checkboxes based on selection limit
    const checkboxes = this.groupList.querySelectorAll('.group-checkbox');
    const groupItems = this.groupList.querySelectorAll('.group-item');

    checkboxes.forEach((checkbox, index) => {
      const isSelected = this.selectedGroups.has(index);
      const shouldDisable = limitReached && !isSelected;

      checkbox.disabled = shouldDisable;

      // Add visual feedback to the group item
      const groupItem = groupItems[index];
      if (shouldDisable) {
        groupItem.classList.add('disabled');
        groupItem.title = `Selection limit reached (${maxGroups} groups max for free plan)`;
      } else {
        groupItem.classList.remove('disabled');
        groupItem.title = '';
      }
    });
  }

  updateSelectAllButton() {
    // Hide "Select All" button for freemium users since it doesn't make sense with 3-group limit
    if (!this.isPremiumUser) {
      this.selectAllBtn.style.display = 'none';
    } else {
      this.selectAllBtn.style.display = '';
    }
  }

  debugGroups() {
    if (!this.currentTab || !this.currentTab.url.includes('facebook.com/groups')) {
      this.updateStatus('⚠️', 'Debug Failed', 'Not on Facebook Groups page', 'warning');
      return;
    }

    this.updateStatus('🔍', 'Debugging', 'Running group name extraction debug...', 'info');

    chrome.tabs.sendMessage(this.currentTab.id, {
      action: "debugGroups"
    }, (response) => {
      if (chrome.runtime.lastError) {
        this.updateStatus('❌', 'Debug Failed', 'Could not run debug. Check console.', 'error');
        return;
      }

      if (response && response.status) {
        this.updateStatus('✅', 'Debug Complete', response.status, 'success');
      }
    });
  }

  async startProcess() {
    if (this.selectedGroups.size === 0) return;

    const settings = this.getSettings();

    // Enforce session limits based on user tier
    const maxGroups = settings.batchSize;
    if (this.selectedGroups.size > maxGroups) {
      if (!this.isPremiumUser && maxGroups === 3) {
        const upgradeConfirmed = confirm(
          `You've selected ${this.selectedGroups.size} groups, but the free plan is limited to ${maxGroups} groups per session.\n\n` +
          `Would you like to upgrade to Premium for unlimited processing?`
        );
        if (upgradeConfirmed) {
          this.showUpgradeModal();
          return;
        } else {
          // Limit selection to max allowed
          const selectedArray = Array.from(this.selectedGroups);
          this.selectedGroups.clear();
          selectedArray.slice(0, maxGroups).forEach(index => {
            this.selectedGroups.add(index);
          });
          this.updateGroupCheckboxes();
          this.updateStartButton();

          alert(`Selection limited to ${maxGroups} groups for the free plan.`);
        }
      } else {
        alert(`Session limit exceeded. Maximum ${maxGroups} groups per session.`);
        return;
      }
    }

    // Show confirmation dialog if enabled
    console.log('=== CONFIRMATION DIALOG CHECK ===');
    console.log('settings.confirmBeforeStart value:', settings.confirmBeforeStart, '(type:', typeof settings.confirmBeforeStart, ')');
    console.log('Will show confirmation dialog?', !!settings.confirmBeforeStart);

    if (settings.confirmBeforeStart) {
      console.log('🔔 Showing confirmation dialog for', this.selectedGroups.size, 'groups');
      const confirmed = confirm(`Are you sure you want to leave ${this.selectedGroups.size} groups? This action cannot be undone.`);
      if (!confirmed) {
        console.log('❌ User cancelled the operation');
        return;
      }
      console.log('✅ User confirmed the operation');
    } else {
      console.log('🚫 Confirmation dialog disabled, proceeding without confirmation');
    }

    this.isProcessing = true;
    this.isPaused = false;
    this.startTime = Date.now();

    // Reset stats
    this.processStats = {
      total: this.selectedGroups.size,
      processed: 0,
      left: 0,
      skipped: 0,
      failed: 0
    };

    // Update UI
    this.updateStatus('🚀', 'Processing', 'Starting group leaving process...', 'info');
    this.progressSection.classList.remove('hidden');
    this.startButton.classList.add('hidden');
    this.pauseButton.classList.remove('hidden');
    this.stopButton.classList.remove('hidden');

    // Start ETA tracking
    this.startETAUpdates();

    // Get selected group data
    const selectedGroupData = Array.from(this.selectedGroups).map(index => this.availableGroups[index]);

    // Send to content script
    chrome.tabs.sendMessage(this.currentTab.id, {
      action: "startLeavingGroups",
      groups: selectedGroupData,
      settings: settings
    }, (response) => {
      if (chrome.runtime.lastError) {
        this.updateStatus('❌', 'Error', 'Failed to start process. Refresh the page and try again.', 'error');
        this.resetProcessUI();
        return;
      }

      console.log('Process started:', response);
    });
  }

  pauseProcess() {
    this.isPaused = true;
    this.pauseButton.classList.add('hidden');
    this.resumeButton.classList.remove('hidden');

    chrome.tabs.sendMessage(this.currentTab.id, { action: "pauseProcess" });
    this.updateStatus('⏸️', 'Paused', 'Process paused by user', 'warning');
  }

  resumeProcess() {
    this.isPaused = false;
    this.resumeButton.classList.add('hidden');
    this.pauseButton.classList.remove('hidden');

    chrome.tabs.sendMessage(this.currentTab.id, { action: "resumeProcess" });
    this.updateStatus('▶️', 'Resuming', 'Process resumed', 'info');
  }

  stopProcess() {
    const confirmed = confirm('Are you sure you want to stop the process?');
    if (!confirmed) return;

    this.isProcessing = false;
    this.isPaused = false;

    chrome.tabs.sendMessage(this.currentTab.id, { action: "stopProcess" });
    this.updateStatus('⏹️', 'Stopped', 'Process stopped by user', 'warning');
    this.resetProcessUI();
    this.saveToHistory('Stopped by user');
  }

  resetProcessUI() {
    this.isProcessing = false;
    this.isPaused = false;

    this.startButton.classList.remove('hidden');
    this.pauseButton.classList.add('hidden');
    this.resumeButton.classList.add('hidden');
    this.stopButton.classList.add('hidden');

    // Stop ETA tracking and hide delay controls
    this.stopETAUpdates();
    this.hideDelayControls();

    this.updateStartButton();
  }

  handleMessage(message, _sender, _sendResponse) {
    switch (message.action) {
      case 'delayUpdate':
        this.handleDelayUpdate(message.data);
        break;

      case 'updateStatus':
        this.updateStatus('ℹ️', 'Processing', message.text, 'info');
        break;

      case 'updateProgress':
        this.updateProgress(message.data);
        break;

      case 'processComplete':
        this.processComplete(message.data);
        break;

      case 'processError':
        this.updateStatus('❌', 'Error', message.error, 'error');
        this.resetProcessUI();
        break;

      case 'currentAction':
        this.currentAction.textContent = message.text;
        // Only show in top progress indicator for "Waiting for new groups to load" messages
        if (message.text && message.text.includes('Waiting for new groups to load')) {
          // Skip showing this message as we're removing it per requirements
        }
        break;

      case 'tierMessage':
        // Handle tier-specific messages from content script
        this.updateStatus('ℹ️', 'Groups Found', message.text, 'info');
        break;
    }
  }

  updateProgress(data) {
    this.processStats = { ...this.processStats, ...data };

    const { total, processed, left, skipped, failed } = this.processStats;
    const percentage = total > 0 ? (processed / total) * 100 : 0;

    // Update progress bar
    this.progressFill.style.width = `${percentage}%`;

    // Update progress percentage
    if (this.progressPercentage) {
      this.progressPercentage.textContent = `${Math.round(percentage)}%`;
    }

    // Update stats
    this.progressStats.textContent = `${processed} / ${total} processed`;
    this.leftCount.textContent = left;
    this.skippedCount.textContent = skipped;
    this.failedCount.textContent = failed;

    // Update time with estimated completion
    if (this.startTime) {
      const elapsed = Date.now() - this.startTime;
      const minutes = Math.floor(elapsed / 60000);
      const seconds = Math.floor((elapsed % 60000) / 1000);

      // Calculate estimated time remaining
      let timeDisplay = `${minutes}:${seconds.toString().padStart(2, '0')}`;

      if (processed > 0 && processed < total) {
        const avgTimePerGroup = elapsed / processed;
        const remainingGroups = total - processed;
        const estimatedRemaining = remainingGroups * avgTimePerGroup;
        const etaMinutes = Math.floor(estimatedRemaining / 60000);
        const etaSeconds = Math.floor((estimatedRemaining % 60000) / 1000);

        timeDisplay += ` (ETA: ${etaMinutes}:${etaSeconds.toString().padStart(2, '0')})`;
      }

      this.progressTime.textContent = timeDisplay;
    }
  }

  processComplete(data) {
    this.isProcessing = false;
    this.updateProgress(data);

    const { left, skipped, failed } = this.processStats;
    this.updateStatus('✅', 'Complete', `Left ${left} groups, skipped ${skipped}, failed ${failed}`, 'success');

    this.resetProcessUI();
    this.hideTopProgressIndicator();

    // Enhanced history saving with detailed group information
    this.saveToHistory('Completed successfully', data);

    // Track which groups were successfully left for better refresh handling
    if (data.leftGroups) {
      data.leftGroups.forEach(groupName => {
        this.leftGroups.add(groupName);
      });
    }

    // Implement intelligent refresh with multiple attempts
    this.intelligentRefresh();
  }

  async intelligentRefresh() {
    const maxRefreshAttempts = 3; // Reduced since DOM removal provides immediate feedback
    const baseDelay = 2000; // Reduced delay since we're removing elements immediately

    this.updateStatus('🔄', 'Refreshing', 'Updating group list to reflect changes...', 'info');

    const attemptRefresh = async (attempt) => {
      console.log(`Refresh attempt ${attempt}/${maxRefreshAttempts}`);

      // Shorter delay since DOM elements are already removed
      const delay = baseDelay + (attempt * 1000);
      await new Promise(resolve => setTimeout(resolve, delay));

      // Store the previous group count for comparison
      const previousGroupCount = this.availableGroups.length;

      // Refresh the groups
      await this.refreshGroups();

      // Check if the refresh was successful by comparing group counts
      const currentGroupCount = this.availableGroups.length;
      const expectedReduction = this.processStats.left;

      console.log(`Previous groups: ${previousGroupCount}, Current groups: ${currentGroupCount}, Expected reduction: ${expectedReduction}`);

      // Since we're removing DOM elements immediately, we expect the group count to be reduced
      // But Facebook might still show some groups that haven't been updated server-side
      if (attempt < maxRefreshAttempts && expectedReduction > 0) {
        // Check if any of the left groups are still in the list
        const stillPresent = this.availableGroups.some(group =>
          this.leftGroups.has(group.name)
        );

        if (stillPresent && currentGroupCount >= previousGroupCount) {
          // Only retry if groups are still present AND the count hasn't decreased
          this.updateStatus('🔄', 'Refreshing', `Waiting for Facebook to sync (attempt ${attempt + 1}/${maxRefreshAttempts})...`, 'info');
          return attemptRefresh(attempt + 1);
        }
      }

      // Final status update
      if (expectedReduction > 0) {
        const actualReduction = Math.max(0, previousGroupCount - currentGroupCount);
        if (actualReduction > 0 || currentGroupCount < previousGroupCount) {
          this.updateStatus('✅', 'Updated', `Group list updated - ${expectedReduction} groups processed`, 'success');
        } else {
          this.updateStatus('ℹ️', 'Processed', `${expectedReduction} groups left (Facebook may take time to update the list)`, 'info');
        }
      } else {
        this.updateStatus('✅', 'Complete', 'Process completed successfully', 'success');
      }
    };

    return attemptRefresh(1);
  }

  getSettings() {
    console.log('=== GET SETTINGS ===');
    console.log('Current checkbox states when getSettings() called:');
    console.log('- confirmBeforeStart element exists:', !!this.confirmBeforeStart);
    console.log('- confirmBeforeStart.checked:', this.confirmBeforeStart ? this.confirmBeforeStart.checked : 'N/A');
    console.log('- skipAdminGroups element exists:', !!this.skipAdminGroups);
    console.log('- skipAdminGroups.checked:', this.skipAdminGroups ? this.skipAdminGroups.checked : 'N/A');

    // Simplified settings - premium users get fast settings automatically
    const delays = this.isPremiumUser ? { actionDelay: 0, confirmDelay: 0 } : { actionDelay: 3000, confirmDelay: 5000 };
    const sessionLimits = this.isPremiumUser ? { maxGroups: 999 } : { maxGroups: 3 };

    // Read current checkbox states (these should reflect saved settings after loadSettings())
    const settings = {
      batchSize: sessionLimits.maxGroups,
      actionDelay: delays.actionDelay,
      confirmDelay: delays.confirmDelay,
      retryAttempts: 0, // Legacy feature removed
      autoScroll: this.getPremiumFeatureValue('autoScroll', true, false),
      confirmBeforeStart: this.confirmBeforeStart ? this.confirmBeforeStart.checked : true,
      skipRecentlyActive: false, // Legacy feature removed
      skipAdminGroups: this.skipAdminGroups ? this.skipAdminGroups.checked : true,
      detailedAnalytics: false, // Legacy feature removed
      isPremium: this.isPremiumUser
    };

    console.log('📤 getSettings() returning:', settings);
    console.log('- confirmBeforeStart in settings:', settings.confirmBeforeStart);
    return settings;
  }



  getPremiumFeatureValue(featureId, premiumValue, freeValue) {
    if (this.isPremiumUser) {
      const element = document.getElementById(featureId);
      return element ? element.checked : premiumValue;
    }
    return freeValue;
  }



  showUpgradeModal(feature = null) {
    // TODO: Replace with actual upgrade/payment flow
    let title = '🚀 Upgrade to Premium!';
    let specificFeature = '';

    // Add contextual messaging based on the feature that triggered the modal
    switch (feature) {
      case 'sort-by-date':
        title = '📅 Sort by Date - Premium Feature';
        specificFeature = '🎯 You tried to sort by recent activity!\n\n' +
                         'Premium users can sort groups by:\n' +
                         '• Most recent activity first\n' +
                         '• Oldest activity first\n' +
                         '• See when you last visited each group\n\n';
        break;
      case 'activity-filter':
        title = '🔍 Activity Filter - Premium Feature';
        specificFeature = '🎯 You tried to filter by activity status!\n\n' +
                         'Premium users can filter groups by:\n' +
                         '• Active groups only\n' +
                         '• Inactive groups only\n' +
                         '• See detailed activity information\n\n';
        break;
      case 'selection-filter':
        title = '✅ Selection Filter - Premium Feature';
        specificFeature = '🎯 You tried to filter by selection status!\n\n' +
                         'Premium users can filter groups by:\n' +
                         '• Selected groups only\n' +
                         '• Unselected groups only\n' +
                         '• Quickly manage large group lists\n\n';
        break;
    }

    alert(
      title + '\n\n' +
      specificFeature +
      'Buy once, use forever - No subscription!\n\n' +
      '✓ Automatic scrolling & discovery\n' +
      '✓ Unlimited groups per session\n' +
      '✓ Faster processing without delays\n' +
      '✓ Background processing when switching tabs\n' +
      '✓ Advanced search, filter & sort options\n' +
      '✓ Detailed session history with clickable URLs\n\n' +
      'One-time payment: $9\n' +
      'Contact us to upgrade your account!'
    );
  }

  // Upgrade prompt management methods
  showSelectionLimitUpgradePrompt() {
    if (this.selectionLimitUpgradePrompt) {
      this.selectionLimitUpgradePrompt.classList.remove('hidden');
    }
  }

  hideSelectionLimitUpgradePrompt() {
    if (this.selectionLimitUpgradePrompt) {
      this.selectionLimitUpgradePrompt.classList.add('hidden');
    }
  }

  showTabSwitchUpgradePrompt() {
    if (this.tabSwitchUpgradePrompt) {
      this.tabSwitchUpgradePrompt.classList.remove('hidden');
    }
  }

  hideTabSwitchUpgradePrompt() {
    if (this.tabSwitchUpgradePrompt) {
      this.tabSwitchUpgradePrompt.classList.add('hidden');
    }
  }

  updateHeroSectionForLimitState(limitReached) {
    // Only update hero section if groups are loaded (we're on the right page)
    if (!this.availableGroups || this.availableGroups.length === 0) {
      return;
    }

    const navigateButton = this.navigateButton;
    const heroUpgradeButton = this.heroUpgradeButton;

    if (this.isPremiumUser) {
      // Premium users: hide both buttons, show success state
      navigateButton.classList.add('hidden');
      heroUpgradeButton.classList.add('hidden');

      const groupCount = this.availableGroups.length;
      this.updateStatus('✅', 'Groups Loaded', `Found ${groupCount} groups - select which ones to leave`, 'success');

      const heroCard = this.heroSection.querySelector('.hero-status-card');
      heroCard.style.borderLeftColor = '#23A094';
      heroCard.style.background = '#F0FDF4';
    } else if (limitReached) {
      // Free users who reached 3-group limit: show upgrade button with urgent styling
      navigateButton.classList.add('hidden');
      heroUpgradeButton.classList.remove('hidden');

      this.updateStatus('🔒', 'Selection limit reached', 'Free plan limited to 3 groups per session. Upgrade for unlimited processing!', 'error');

      // Override styling for limit reached state (more urgent)
      const heroCard = this.heroSection.querySelector('.hero-status-card');
      heroCard.style.borderLeftColor = '#DC2626';
      heroCard.style.background = 'linear-gradient(135deg, #FEF3F2 0%, #FFFBFA 100%)';
    } else {
      // Free users with groups loaded but under limit: show upgrade button with normal styling
      navigateButton.classList.add('hidden');
      heroUpgradeButton.classList.remove('hidden');

      const groupCount = this.availableGroups.length;
      this.updateStatus('ℹ️', 'Groups Found', `Found ${groupCount} visible groups. Scroll down manually to see more, or upgrade to Premium for automatic discovery.`, 'info');

      // Use info styling for normal upgrade prompt
      const heroCard = this.heroSection.querySelector('.hero-status-card');
      heroCard.style.borderLeftColor = '#FF90E8';
      heroCard.style.background = '#F4F4F0';
    }
  }



  // Tab visibility detection for background processing upgrade prompts
  setupTabVisibilityDetection() {
    // Listen for visibility change events
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isProcessing && !this.isPremiumUser) {
        // User switched away from tab during processing - show upgrade prompt
        this.showTabSwitchUpgradePrompt();
        // Pause processing for freemium users
        if (!this.isPaused) {
          this.pauseProcess();
        }
      }
    });

    // Listen for beforeunload events (user trying to close/navigate away)
    window.addEventListener('beforeunload', (event) => {
      if (this.isProcessing && !this.isPremiumUser) {
        // Show upgrade prompt for background processing
        this.showTabSwitchUpgradePrompt();
        event.preventDefault();
        event.returnValue = 'Processing is in progress. Premium users can continue processing in background.';
        return event.returnValue;
      }
    });
  }

  updatePremiumFeatureStates() {
    // Update tier display based on user status
    this.updateTierDisplay();

    // Update session limit indicator
    if (this.sessionLimitIndicator) {
      if (this.isPremiumUser) {
        this.sessionLimitIndicator.style.display = 'inline-flex';
        // Update session limit display
        const sessionLimitValue = document.getElementById('sessionLimit');
        if (sessionLimitValue) {
          sessionLimitValue.textContent = '∞';
        }
      } else {
        this.sessionLimitIndicator.style.display = 'none';
        // Update session limit display
        const sessionLimitValue = document.getElementById('sessionLimit');
        if (sessionLimitValue) {
          sessionLimitValue.textContent = '3';
        }
      }
    }

    // Enable/disable premium features based on user status
    const premiumFeatures = document.querySelectorAll('.premium-disabled');
    premiumFeatures.forEach(element => {
      const checkbox = element.querySelector('input[type="checkbox"]');
      if (checkbox) {
        checkbox.disabled = !this.isPremiumUser;
      }

      if (this.isPremiumUser) {
        element.classList.remove('premium-disabled');
        element.style.opacity = '1';
        element.style.cursor = 'pointer';
      } else {
        element.classList.add('premium-disabled');
        // Remove inline styles to let CSS handle the styling
        element.style.opacity = '';
        element.style.cursor = '';
      }
    });

    // Handle premium feature controls specifically
    const premiumControls = [
      this.activityFilterSelect,
      this.selectionFilterSelect,
      this.sortActivityBtn
    ];

    premiumControls.forEach(control => {
      if (control) {
        if (this.isPremiumUser) {
          control.disabled = false;
          control.parentElement.classList.remove('premium-disabled');
        } else {
          control.disabled = true;
          control.parentElement.classList.add('premium-disabled');
        }
      }
    });

    // Handle search/filter premium overlays
    const premiumOverlays = [
      this.activityFilterOverlay,
      this.selectionFilterOverlay,
      this.sortActivityOverlay
    ];

    premiumOverlays.forEach(overlay => {
      if (overlay) {
        if (this.isPremiumUser) {
          overlay.style.display = 'none';
        } else {
          overlay.style.display = 'flex';
        }
      }
    });



    // Update sort button tooltips and states based on premium status
    this.updateSortButtons();
  }

  updateTierDisplay() {
    // Update the tier comparison display based on user's premium status
    const freeColumn = document.querySelector('.tier-free');
    const premiumColumn = document.querySelector('.tier-premium');
    const freeCta = document.querySelector('.tier-free .btn-tier');
    const premiumCta = document.querySelector('.tier-premium .btn-tier');

    if (!freeColumn || !premiumColumn) return;

    if (this.isPremiumUser) {
      // User has premium - update display
      if (freeCta) {
        freeCta.textContent = 'Previous Plan';
        freeCta.disabled = true;
        freeCta.classList.remove('btn-secondary');
        freeCta.classList.add('btn-secondary');
      }

      if (premiumCta) {
        premiumCta.innerHTML = '<span class="btn-icon">✓</span> Current Plan';
        premiumCta.disabled = true;
        premiumCta.classList.remove('btn-premium');
        premiumCta.classList.add('btn-success');
      }

      // Add visual indicator to premium column
      premiumColumn.style.borderColor = '#23A094';
      premiumColumn.style.background = 'linear-gradient(135deg, #F0FDF4 0%, #FFFFFF 100%)';
    } else {
      // User is on free tier - keep upgrade CTA
      if (freeCta) {
        freeCta.textContent = 'Current Plan';
        freeCta.disabled = true;
      }

      if (premiumCta) {
        premiumCta.innerHTML = '<span class="btn-icon">⭐</span> Upgrade to Premium';
        premiumCta.disabled = false;
        premiumCta.classList.remove('btn-success');
        premiumCta.classList.add('btn-premium');
      }
    }
  }





  loadSettings() {
    console.log('=== LOADING SETTINGS FROM chrome.storage.local ===');
    chrome.storage.local.get('groupLeaverSettings', (result) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Error loading settings from chrome.storage.local:', chrome.runtime.lastError);
        this.setDefaultSettings();
        return;
      }

      console.log('📦 Raw storage result from chrome.storage.local:', result);

      if (result.groupLeaverSettings) {
        this.applyLoadedSettings(result.groupLeaverSettings);
      } else {
        console.log('⚠️ No saved settings found in chrome.storage.local, checking chrome.storage.sync for migration...');
        // Check if there are old settings in chrome.storage.sync to migrate
        this.migrateFromSyncStorage();
      }
    });
  }

  migrateFromSyncStorage() {
    console.log('🔄 Checking chrome.storage.sync for existing settings to migrate...');
    chrome.storage.sync.get('groupLeaverSettings', (result) => {
      if (chrome.runtime.lastError) {
        console.log('No chrome.storage.sync access or error:', chrome.runtime.lastError);
        this.setDefaultSettings();
        return;
      }

      if (result.groupLeaverSettings) {
        console.log('📦 Found settings in chrome.storage.sync, migrating to chrome.storage.local:', result.groupLeaverSettings);

        // Save to local storage
        chrome.storage.local.set({ groupLeaverSettings: result.groupLeaverSettings }, () => {
          if (chrome.runtime.lastError) {
            console.error('❌ Error migrating settings to chrome.storage.local:', chrome.runtime.lastError);
            this.setDefaultSettings();
          } else {
            console.log('✅ Settings migrated successfully to chrome.storage.local');
            // Apply the migrated settings
            this.applyLoadedSettings(result.groupLeaverSettings);

            // Clean up old sync storage
            chrome.storage.sync.remove('groupLeaverSettings', () => {
              console.log('🗑️ Cleaned up old settings from chrome.storage.sync');
            });
          }
        });
      } else {
        console.log('⚠️ No settings found in chrome.storage.sync either, setting defaults');
        this.setDefaultSettings();
      }
    });
  }

  applyLoadedSettings(settings) {
    console.log('📥 Applying loaded settings:', settings);
    console.log('- settings.confirmBeforeStart value:', settings.confirmBeforeStart, '(type:', typeof settings.confirmBeforeStart, ')');
    console.log('- settings.skipAdminGroups value:', settings.skipAdminGroups, '(type:', typeof settings.skipAdminGroups, ')');

    // Load essential settings (always available) - ensure proper boolean conversion
    const confirmValue = settings.confirmBeforeStart === undefined ? true : settings.confirmBeforeStart;
    const skipAdminValue = settings.skipAdminGroups === undefined ? true : settings.skipAdminGroups;

    console.log('🔄 Setting checkbox values:');
    console.log('- confirmBeforeStart will be set to:', confirmValue);
    console.log('- skipAdminGroups will be set to:', skipAdminValue);

    this.confirmBeforeStart.checked = confirmValue;
    this.skipAdminGroups.checked = skipAdminValue;

    console.log('✅ After setting - checkbox states:');
    console.log('- confirmBeforeStart.checked:', this.confirmBeforeStart.checked);
    console.log('- skipAdminGroups.checked:', this.skipAdminGroups.checked);

    // Load premium settings (only if premium user)
    if (this.isPremiumUser) {
      if (this.autoScroll) {
        this.autoScroll.checked = settings.autoScroll === undefined ? true : settings.autoScroll;
      }
      // Legacy features (removed from UI but kept for backward compatibility):
      // - skipRecentlyActive, retryFailedGroups, detailedAnalytics
    }

    console.log('✅ Settings applied successfully');

    // Update UI displays
    this.updatePremiumFeatureStates();
  }

  setDefaultSettings() {
    console.log('Setting default settings');

    // Set default essential settings (always available)
    this.confirmBeforeStart.checked = true;
    this.skipAdminGroups.checked = true;

    // Set default premium settings (only if premium user)
    if (this.isPremiumUser) {
      if (this.autoScroll) this.autoScroll.checked = true;
      // Legacy features (removed from UI but kept for compatibility):
      // - skipRecentlyActive, retryFailedGroups, detailedAnalytics
    }

    console.log('Default settings applied - confirmBeforeStart:', this.confirmBeforeStart.checked, 'skipAdminGroups:', this.skipAdminGroups.checked);
    this.updatePremiumFeatureStates();
  }

  saveSettings() {
    console.log('=== MANUAL SAVE SETTINGS (Legacy) ===');
    console.log('Note: Auto-save is now enabled, but manual save still works for compatibility');

    // Use the auto-save method for consistency
    this.autoSaveSettings();

    // Show legacy status message for any remaining manual save calls
    this.updateStatus('💾', 'Settings Saved', 'Your preferences have been saved automatically', 'success');
    setTimeout(() => this.checkCurrentPage(), 1000);
  }

  // Auto-save functionality
  setupAutoSaveListeners() {
    console.log('🔧 Setting up auto-save listeners for essential settings');

    // Add change listeners to essential settings checkboxes
    if (this.confirmBeforeStart) {
      this.confirmBeforeStart.addEventListener('change', () => {
        console.log('📝 confirmBeforeStart changed to:', this.confirmBeforeStart.checked);
        this.autoSaveSettings();
      });
    }

    if (this.skipAdminGroups) {
      this.skipAdminGroups.addEventListener('change', () => {
        console.log('📝 skipAdminGroups changed to:', this.skipAdminGroups.checked);
        this.autoSaveSettings();
      });
    }

    // Add listeners for premium settings if user is premium
    if (this.isPremiumUser) {
      if (this.autoScroll) {
        this.autoScroll.addEventListener('change', () => {
          console.log('📝 autoScroll changed to:', this.autoScroll.checked);
          this.autoSaveSettings();
        });
      }

      // Legacy features (removed from UI but kept for compatibility):
      // - skipRecentlyActive, retryFailedGroups, detailedAnalytics
      // No longer adding event listeners for these removed features
    }
  }

  autoSaveSettings() {
    console.log('💾 Auto-saving settings...');

    // Create settings object with current checkbox states
    const settings = {
      // Essential settings (always available)
      confirmBeforeStart: this.confirmBeforeStart.checked,
      skipAdminGroups: this.skipAdminGroups.checked,

      // Premium settings (only save if premium user)
      autoScroll: this.isPremiumUser && this.autoScroll ? this.autoScroll.checked : true,
      skipRecentlyActive: false, // Legacy feature removed
      retryAttempts: 0, // Legacy feature removed
      detailedAnalytics: false, // Legacy feature removed

      // Additional settings for processing
      batchSize: this.isPremiumUser ? 999 : 3,
      actionDelay: this.isPremiumUser ? 0 : 3000,
      confirmDelay: this.isPremiumUser ? 0 : 5000,
      isPremium: this.isPremiumUser
    };

    console.log('💾 Auto-saving settings object:', settings);

    chrome.storage.local.set({ groupLeaverSettings: settings }, () => {
      if (chrome.runtime.lastError) {
        console.error('❌ Error auto-saving settings to chrome.storage.local:', chrome.runtime.lastError);
      } else {
        console.log('✅ Settings auto-saved successfully to chrome.storage.local:', settings);
      }
    });
  }



  // Debug method to check storage contents
  debugStorage() {
    console.log('=== STORAGE DEBUG (chrome.storage.local) ===');
    chrome.storage.local.get('groupLeaverSettings', (result) => {
      console.log('📦 Current chrome.storage.local contents:', result);
      if (result.groupLeaverSettings) {
        console.log('✅ Settings found in storage:');
        console.log('- confirmBeforeStart in storage:', result.groupLeaverSettings.confirmBeforeStart, '(type:', typeof result.groupLeaverSettings.confirmBeforeStart, ')');
        console.log('- skipAdminGroups in storage:', result.groupLeaverSettings.skipAdminGroups, '(type:', typeof result.groupLeaverSettings.skipAdminGroups, ')');
        console.log('- Full settings object:', result.groupLeaverSettings);
      } else {
        console.log('❌ No settings found in chrome.storage.local');
      }
    });
  }

  // Test function to verify the complete save/load cycle
  testSettingsPersistence() {
    console.log('=== TESTING AUTO-SAVE SETTINGS PERSISTENCE ===');

    // Test 1: Auto-save confirmBeforeStart as false
    console.log('🧪 Test 1: Setting confirmBeforeStart to false (should auto-save)...');
    this.confirmBeforeStart.checked = false;
    // Trigger change event to test auto-save
    this.confirmBeforeStart.dispatchEvent(new Event('change'));

    // Test 2: Simulate reload by clearing checkboxes and reloading
    setTimeout(() => {
      console.log('🧪 Test 2: Simulating reload - clearing checkboxes and reloading settings...');
      this.confirmBeforeStart.checked = true; // Reset to opposite value
      this.skipAdminGroups.checked = false; // Reset to opposite value

      console.log('Before reload - checkbox states:');
      console.log('- confirmBeforeStart.checked:', this.confirmBeforeStart.checked);
      console.log('- skipAdminGroups.checked:', this.skipAdminGroups.checked);

      // Reload settings
      this.loadSettings();

      // Test 3: Check final state after a delay
      setTimeout(() => {
        console.log('🧪 Test 3: Final verification after reload...');
        console.log('After reload - checkbox states:');
        console.log('- confirmBeforeStart.checked:', this.confirmBeforeStart.checked);
        console.log('- skipAdminGroups.checked:', this.skipAdminGroups.checked);

        if (this.confirmBeforeStart.checked === false) {
          console.log('✅ SUCCESS: Auto-save confirmBeforeStart persistence test PASSED');
        } else {
          console.log('❌ FAILURE: Auto-save confirmBeforeStart persistence test FAILED');
        }

        this.debugStorage();
      }, 500);
    }, 1000); // Longer delay to allow auto-save to complete
  }



  saveToHistory(status, sessionData = null) {
    const historyItem = {
      date: new Date().toISOString(),
      stats: { ...this.processStats },
      status: status,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      sessionId: Date.now() + Math.random(), // Unique session identifier
      groups: {
        successful: [],
        skipped: [],
        failed: []
      }
    };

    // Enhanced: Capture detailed group information if available
    if (sessionData) {
      // Get the groups that were processed in this session
      const processedGroups = this.selectedGroups.size > 0 ?
        Array.from(this.selectedGroups).map(index => this.availableGroups[index]) : [];

      // Categorize groups based on session results
      if (sessionData.leftGroups) {
        historyItem.groups.successful = sessionData.leftGroups.map(groupName => ({
          name: groupName,
          url: this.getGroupUrlByName(groupName) || this.generateGroupUrl(groupName)
        }));
      }

      if (sessionData.skippedGroups) {
        historyItem.groups.skipped = sessionData.skippedGroups.map(groupName => ({
          name: groupName,
          url: this.getGroupUrlByName(groupName) || this.generateGroupUrl(groupName),
          reason: 'Admin or recently active'
        }));
      }

      if (sessionData.failedGroups) {
        historyItem.groups.failed = sessionData.failedGroups.map(groupName => ({
          name: groupName,
          url: this.getGroupUrlByName(groupName) || this.generateGroupUrl(groupName),
          reason: 'Technical error'
        }));
      }

      // If detailed group data isn't available, infer from selected groups and stats
      if (!sessionData.leftGroups && !sessionData.skippedGroups && !sessionData.failedGroups && processedGroups.length > 0) {
        // Fallback: distribute processed groups based on stats
        let leftCount = this.processStats.left;
        let skippedCount = this.processStats.skipped;
        let failedCount = this.processStats.failed;

        processedGroups.forEach((group, index) => {
          if (leftCount > 0) {
            historyItem.groups.successful.push({
              name: group.name,
              url: group.url || this.generateGroupUrl(group.name)
            });
            leftCount--;
          } else if (skippedCount > 0) {
            historyItem.groups.skipped.push({
              name: group.name,
              url: group.url || this.generateGroupUrl(group.name),
              reason: 'Skipped during processing'
            });
            skippedCount--;
          } else if (failedCount > 0) {
            historyItem.groups.failed.push({
              name: group.name,
              url: group.url || this.generateGroupUrl(group.name),
              reason: 'Processing failed'
            });
            failedCount--;
          }
        });
      }
    }

    chrome.storage.local.get('groupLeaverHistory', (result) => {
      const history = result.groupLeaverHistory || [];
      history.unshift(historyItem);

      // Keep only last 50 entries
      if (history.length > 50) {
        history.splice(50);
      }

      chrome.storage.local.set({ groupLeaverHistory: history }, () => {
        this.loadHistory();
      });
    });
  }

  // Helper method to get actual group URL by name from available groups
  getGroupUrlByName(groupName) {
    if (!this.availableGroups) return null;

    const group = this.availableGroups.find(g => g.name === groupName);
    return group ? group.url : null;
  }

  // Helper method to generate Facebook group URLs (fallback)
  generateGroupUrl(groupName) {
    // Fallback search URL when actual group URL is not available
    return `https://www.facebook.com/groups/search/?q=${encodeURIComponent(groupName)}`;
  }

  loadHistory() {
    chrome.storage.local.get('groupLeaverHistory', (result) => {
      const history = result.groupLeaverHistory || [];
      this.displayHistory(history);
    });
  }

  displayHistory(history) {
    if (history.length === 0) {
      this.historyList.innerHTML = '<div class="history-empty">No leaving sessions yet</div>';
      return;
    }

    this.historyList.innerHTML = '';

    history.forEach((item, index) => {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-session';

      const date = new Date(item.date);
      const duration = item.duration ? Math.floor(item.duration / 1000) : 0;

      // Count total groups in this session
      const totalGroups = (item.groups?.successful?.length || 0) +
                         (item.groups?.skipped?.length || 0) +
                         (item.groups?.failed?.length || 0);

      historyItem.innerHTML = `
        <div class="session-header">
          <div class="session-main-info">
            <div class="session-date">${date.toLocaleString()}</div>
            <div class="session-summary">
              <span class="session-status">${item.status}</span>
              <span class="session-stats">
                <span class="stat-success">${item.stats.left} left</span>
                <span class="stat-skipped">${item.stats.skipped} skipped</span>
                <span class="stat-failed">${item.stats.failed} failed</span>
              </span>
            </div>
            ${duration > 0 ? `<div class="session-duration">${duration}s</div>` : ''}
          </div>
          <div class="session-toggle">
            <span class="toggle-icon">▼</span>
            <span class="toggle-text">View Details</span>
          </div>
        </div>
        <div class="session-details">
          ${this.renderSessionGroups(item.groups)}
        </div>
      `;

      // Add click event listener for expanding/collapsing
      const sessionHeader = historyItem.querySelector('.session-header');
      const toggleText = historyItem.querySelector('.toggle-text');

      sessionHeader.addEventListener('click', () => {
        const isExpanded = historyItem.classList.toggle('expanded');
        // Update toggle text based on state
        toggleText.textContent = isExpanded ? 'Hide Details' : 'View Details';
      });

      this.historyList.appendChild(historyItem);
    });
  }

  renderSessionGroups(groups) {
    if (!groups) {
      return '<div class="no-group-details">No detailed group information available for this session.</div>';
    }

    let html = '';

    // Successful groups
    if (groups.successful && groups.successful.length > 0) {
      html += `
        <div class="group-category success">
          <div class="category-header">
            <span class="category-icon">✅</span>
            <span class="category-title">Successfully Left (${groups.successful.length})</span>
          </div>
          <div class="group-list">
            ${groups.successful.map(group => `
              <div class="group-item-detail">
                <span class="group-name">${group.name}</span>
                <a href="${group.url}" target="_blank" class="group-link" title="Search for this group">🔗</a>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }

    // Skipped groups
    if (groups.skipped && groups.skipped.length > 0) {
      html += `
        <div class="group-category skipped">
          <div class="category-header">
            <span class="category-icon">⏭️</span>
            <span class="category-title">Skipped (${groups.skipped.length})</span>
          </div>
          <div class="group-list">
            ${groups.skipped.map(group => `
              <div class="group-item-detail">
                <span class="group-name">${group.name}</span>
                <span class="group-reason">${group.reason || 'Unknown reason'}</span>
                <a href="${group.url}" target="_blank" class="group-link" title="Search for this group">🔗</a>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }

    // Failed groups
    if (groups.failed && groups.failed.length > 0) {
      html += `
        <div class="group-category failed">
          <div class="category-header">
            <span class="category-icon">❌</span>
            <span class="category-title">Failed (${groups.failed.length})</span>
          </div>
          <div class="group-list">
            ${groups.failed.map(group => `
              <div class="group-item-detail">
                <span class="group-name">${group.name}</span>
                <span class="group-reason">${group.reason || 'Unknown error'}</span>
                <a href="${group.url}" target="_blank" class="group-link" title="Search for this group">🔗</a>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    }

    return html || '<div class="no-group-details">No groups were processed in this session.</div>';
  }

  clearHistory() {
    const confirmed = confirm('Clear all processing history?');
    if (!confirmed) return;

    chrome.storage.local.remove('groupLeaverHistory', () => {
      this.loadHistory();
      this.updateStatus('🗑️', 'History Cleared', 'Processing history has been cleared', 'info');
    });
  }

  // Enhanced UX methods for delay control
  handleDelayUpdate(data) {
    this.currentDelayInfo = data;

    if (data.type === 'completed') {
      this.hideDelayControls();
      return;
    }

    this.showDelayControls(data);
  }

  showDelayControls(delayData) {
    // Create or update delay controls UI
    if (!this.delayControls) {
      this.createDelayControls();
    }

    const delayInfo = this.delayControls.querySelector('.delay-info');
    const delayProgress = this.delayControls.querySelector('.delay-progress');
    const skipButton = this.delayControls.querySelector('.skip-delay-btn');
    const continueButton = this.delayControls.querySelector('.continue-manual-btn');

    // Update delay information
    if (delayData.isManual) {
      delayInfo.textContent = `Manual Mode: ${delayData.reason}`;
      delayProgress.style.display = 'none';
      skipButton.style.display = 'none';
      continueButton.style.display = 'inline-block';
    } else {
      const seconds = Math.ceil(delayData.remaining / 1000);
      delayInfo.textContent = `${delayData.reason} (${seconds}s remaining)`;

      // Update progress bar
      const maxTime = delayData.remaining + 1000; // Estimate max time
      const progress = Math.max(0, 100 - (delayData.remaining / maxTime * 100));
      delayProgress.style.width = `${progress}%`;
      delayProgress.style.display = 'block';

      skipButton.style.display = delayData.canSkip ? 'inline-block' : 'none';
      continueButton.style.display = 'none';
    }

    this.delayControls.style.display = 'block';
  }

  hideDelayControls() {
    if (this.delayControls) {
      this.delayControls.style.display = 'none';
    }
  }

  createDelayControls() {
    // Create delay controls UI
    this.delayControls = document.createElement('div');
    this.delayControls.className = 'delay-controls';
    this.delayControls.innerHTML = `
      <div class="delay-info"></div>
      <div class="delay-progress-container">
        <div class="delay-progress"></div>
      </div>
      <div class="delay-buttons">
        <button class="btn btn-small skip-delay-btn">Skip Wait</button>
        <button class="btn btn-small continue-manual-btn">Continue</button>
        <button class="btn btn-small toggle-manual-btn">Manual Mode</button>
      </div>
    `;

    // Add event listeners
    this.delayControls.querySelector('.skip-delay-btn').addEventListener('click', () => {
      this.skipCurrentDelay();
    });

    this.delayControls.querySelector('.continue-manual-btn').addEventListener('click', () => {
      this.continueManualStep();
    });

    this.delayControls.querySelector('.toggle-manual-btn').addEventListener('click', () => {
      this.toggleManualMode();
    });

    // Insert after progress section
    this.progressSection.parentNode.insertBefore(this.delayControls, this.progressSection.nextSibling);
  }

  skipCurrentDelay() {
    chrome.tabs.sendMessage(this.currentTab.id, { action: "skipDelay" });
  }

  continueManualStep() {
    chrome.tabs.sendMessage(this.currentTab.id, { action: "continueManual" });
  }

  toggleManualMode() {
    this.manualMode = !this.manualMode;
    chrome.tabs.sendMessage(this.currentTab.id, {
      action: "setManualMode",
      enabled: this.manualMode
    });

    const button = this.delayControls.querySelector('.toggle-manual-btn');
    button.textContent = this.manualMode ? 'Auto Mode' : 'Manual Mode';
    button.classList.toggle('active', this.manualMode);
  }

  startETAUpdates() {
    if (this.etaUpdateInterval) {
      clearInterval(this.etaUpdateInterval);
    }

    this.etaUpdateInterval = setInterval(() => {
      this.updateETA();
    }, 2000); // Update every 2 seconds
  }

  stopETAUpdates() {
    if (this.etaUpdateInterval) {
      clearInterval(this.etaUpdateInterval);
      this.etaUpdateInterval = null;
    }
  }

  updateETA() {
    if (!this.currentTab) return;

    chrome.tabs.sendMessage(this.currentTab.id, { action: "getETA" }, (response) => {
      if (response && response.eta) {
        this.displayETA(response.eta);
      }
    });
  }

  displayETA(eta) {
    if (!this.progressTime) return;

    const etaText = eta ? `ETA: ${eta.etaFormatted} | Elapsed: ${eta.elapsedFormatted}` : 'Calculating...';
    this.progressTime.textContent = etaText;
  }

  // Top progress indicator methods
  showTopProgressIndicator(text, showProgress = false, progressValue = 0) {
    if (!this.topProgressIndicator) return;

    this.topProgressText.textContent = text;
    this.topProgressIndicator.classList.remove('hidden');

    if (showProgress) {
      this.topProgressFill.style.width = `${progressValue}%`;
    } else {
      // Show indeterminate progress animation
      this.topProgressFill.style.width = '100%';
      this.topProgressFill.style.animation = 'pulse 2s infinite';
    }
  }

  hideTopProgressIndicator() {
    if (!this.topProgressIndicator) return;
    this.topProgressIndicator.classList.add('hidden');
    this.topProgressFill.style.animation = '';
  }

  stopAutoDiscovery() {
    // Send message to content script to stop auto-discovery process
    if (this.currentTab) {
      chrome.tabs.sendMessage(this.currentTab.id, { action: "stopAutoDiscovery" });
      this.hideTopProgressIndicator();
      this.updateStatus('⏹️', 'Discovery Stopped', 'Auto-discovery process stopped by user', 'warning');
    }
  }

  // Cleanup method for when popup is closed
  cleanup() {
    if (this.backgroundPort) {
      this.backgroundPort.disconnect();
      this.backgroundPort = null;
    }

    if (this.etaUpdateInterval) {
      clearInterval(this.etaUpdateInterval);
      this.etaUpdateInterval = null;
    }

    if (this.contentChangeTimeout) {
      clearTimeout(this.contentChangeTimeout);
      this.contentChangeTimeout = null;
    }
  }
}

// Initialize the UI when the popup loads
document.addEventListener('DOMContentLoaded', () => {
  const ui = new GroupLeaverUI();

  // Make UI instance available for debugging
  window.groupLeaverUI = ui;

  // Cleanup when popup is closed
  window.addEventListener('beforeunload', () => {
    ui.cleanup();
  });
});