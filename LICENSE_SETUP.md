# License Key Setup Guide

This guide explains how to set up the license key-based premium subscription model for the Facebook Group Leaver extension.

## Overview

The extension now uses Gumroad's license verification API to manage premium subscriptions. Users purchase a license key from Gumroad and activate it in the extension to unlock premium features.

## Setup Steps

### 1. Create Gumroad Product

1. Go to [Gumroad](https://gumroad.com) and create an account
2. Create a new product for "Facebook Group Leaver Premium"
3. Set the price to $9 (one-time purchase)
4. Enable license key generation in the product settings
5. Note down your product ID from the product edit page

### 2. Update Configuration

Update the following files with your actual Gumroad information:

#### `license-manager.js`
```javascript
// Line 10: Replace with your actual product ID
this.productId = 'YOUR_ACTUAL_GUMROAD_PRODUCT_ID';

// Line 270: Replace with your actual product URL
return 'https://gumroad.com/l/YOUR_PRODUCT_PERMALINK';
```

### 3. Test License Verification

1. Create a test purchase on Gumroad
2. Copy the license key from the purchase confirmation
3. Open the extension and go to Settings tab
4. Paste the license key and click "Activate License"
5. Verify that premium features are unlocked

## Features

### License Management
- **Client-side verification**: Uses Gumroad's public API directly from the browser
- **Automatic caching**: Reduces API calls with 24-hour verification cache
- **Persistent storage**: License status saved in `chrome.storage.local`
- **Auto-detection**: Attempts to detect license keys from clipboard

### User Experience
- **Neubrutalism design**: Consistent with Gumroad's design system
- **Clear visual feedback**: Success/error states with appropriate styling
- **Helpful instructions**: Step-by-step activation guide
- **Error handling**: Graceful handling of network issues and invalid keys

### Premium Features Gated
- Automatic scrolling and group discovery
- Unlimited groups per session
- Faster processing without delays
- Background processing when switching tabs
- Advanced search, filter, and sort options
- Detailed session history with clickable URLs

## API Integration

The extension uses Gumroad's license verification endpoint:
```
POST https://api.gumroad.com/v2/licenses/verify
```

Parameters:
- `product_id`: Your Gumroad product ID
- `license_key`: Customer's license key
- `increment_uses_count`: false (for activation, true for usage tracking)

## Security Considerations

- License verification is done client-side using Gumroad's public API
- No sensitive data is stored locally except the license key
- License status is cached for 24 hours to reduce API calls
- Network errors gracefully fall back to cached verification status

## Troubleshooting

### Common Issues

1. **"Invalid license key" error**
   - Verify the product ID is correct
   - Check that the license key was copied correctly
   - Ensure the product has license key generation enabled

2. **Network verification errors**
   - Check internet connection
   - Verify Gumroad API is accessible
   - Extension will use cached status if available

3. **Premium features not unlocking**
   - Refresh the extension popup
   - Check that `isPremiumUser` is being updated correctly
   - Verify license status in browser console

### Debug Mode

Enable debug logging by opening browser console and checking for:
- License manager initialization messages
- Verification API responses
- Premium status updates

## Deployment Checklist

- [ ] Update product ID in `license-manager.js`
- [ ] Update purchase URL in `license-manager.js`
- [ ] Test license verification with real Gumroad product
- [ ] Verify all premium features are properly gated
- [ ] Test upgrade flow from free to premium
- [ ] Confirm neubrutalism styling is consistent
- [ ] Test error handling for invalid licenses
- [ ] Verify clipboard detection works
- [ ] Test license deactivation flow

## Support

For license-related issues, users should:
1. Check their Gumroad purchase confirmation for the correct license key
2. Try the clipboard paste feature if manual entry fails
3. Contact support with their order ID if verification continues to fail

The extension provides clear error messages and troubleshooting guidance in the UI.
