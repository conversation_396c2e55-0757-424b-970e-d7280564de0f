/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Desktop-Only Design - Optimized for Chrome Extension Popup */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  font-weight: 400;
  background: #F4F4F0;
  color: #242423;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden; /* Remove horizontal scrolling only */
  overflow-y: auto; /* Allow vertical scrolling when needed */
  margin: 0;
  padding: 0;
  width: 780px; /* Increased for better space */
  min-height: 620px; /* Minimum height, but allow expansion */
}

.container {
  background: #FFFFFF;
  border-radius: 4px;
  box-shadow: 4px 4px 0px #000000;
  overflow-x: hidden; /* Remove horizontal scrolling only */
  overflow-y: auto; /* Allow vertical scrolling when needed */
  border: 3px solid #000000;
  display: flex;
  flex-direction: column;
  margin: 0;
  width: 100%;
  min-height: 100%; /* Allow expansion beyond initial height */
}

/* Header Styles - Desktop Design - Compact Optimized */
.header {
  background: #FFB3EC; /* Softer pink */
  color: #000000;
  text-align: center;
  border-bottom: 3px solid #000000;
  flex-shrink: 0;
  padding: 8px 16px; /* Reduced from 14px 20px */
}

.header-icon {
  filter: drop-shadow(2px 2px 0px #000000);
  font-size: 18px; /* Reduced from 24px */
  margin-bottom: 4px; /* Reduced from 6px */
}

.header-title {
  font-weight: 800; /* More readable weight */
  letter-spacing: 0.5px; /* More readable spacing */
  text-transform: uppercase;
  font-size: 16px; /* Reduced from 20px */
  margin-bottom: 2px; /* Reduced from 4px */
}

.header-subtitle {
  font-weight: 500; /* More readable weight */
  opacity: 0.85;
  line-height: 1.4;
  font-size: 11px; /* Reduced from 13px */
}

/* Tab Navigation - Desktop Design */
.tabs {
  display: flex;
  background: #F4F4F0;
  border-bottom: 3px solid #000000;
  flex-shrink: 0;
}

.tab-button {
  flex: 1;
  border: none;
  border-right: 3px solid #000000;
  background: transparent;
  cursor: pointer;
  font-weight: 600;
  color: #808080;
  transition: all 0.1s ease;
  position: relative;
  -webkit-tap-highlight-color: transparent;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 14px; /* Reduced from 12px 18px */
  min-height: 32px; /* Reduced from 40px */
  font-size: 12px; /* Reduced from 14px */
  font-weight: 600;
}

.tab-button:last-child {
  border-right: none;
}

.tab-button:hover {
  background: #FFFFFF;
  color: #242423;
  transform: translateY(-1px);
  box-shadow: 0px 3px 0px #000000;
}

.tab-button.active {
  color: #000000;
  background: #FFB3EC;
  transform: translateY(-2px);
  box-shadow: 0px 4px 0px #000000;
  font-weight: 700;
  border: 3px solid #000000; /* Ensure clean border without artifacts */
  border-right: 3px solid #000000; /* Maintain right border consistency */
  position: relative;
  z-index: 1;
  outline: none; /* Remove any focus outline that might cause pink border */
}

/* Tab Content - Desktop Design - Compact Optimized */
.tab-content {
  display: none;
  flex: 1;
  overflow-x: hidden; /* Remove horizontal scrolling only */
  overflow-y: auto; /* Allow vertical scrolling when needed */
  background: #FFFFFF;
  padding: 12px 16px; /* Reduced from 16px 20px */
}

/* Main Tab - Single Column Workflow Layout */
#main-tab.tab-content.active {
  display: flex;
  flex-direction: column;
  gap: 12px; /* Reduced from 16px */
  align-items: stretch;
  min-height: calc(100% - 24px); /* Reduced from 32px */
  overflow-x: hidden;
  overflow-y: auto;
}

/* Settings Tab - Single Column Layout for Comparison */
#settings-tab.tab-content.active {
  display: flex;
  flex-direction: column;
  gap: 10px; /* Reduced from 12px */
  align-items: stretch;
  min-height: calc(100% - 24px); /* Reduced from 32px */
  overflow-x: hidden;
  overflow-y: auto;
}

/* History Tab - Single Column Layout for Enhanced Sessions */
#history-tab.tab-content.active {
  display: block;
  padding: 12px 16px; /* Reduced from 16px 20px */
  min-height: calc(100% - 24px); /* Reduced from 32px */
}

/* Single column fallback for other tabs */
.tab-content.active {
  display: block;
}

/* Hero Section - Prominent status and navigation */
.hero-section {
  flex-shrink: 0;
}

.hero-status-card {
  background: #F4F4F0;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #FF90E8;
  padding: 14px 18px; /* Reduced from 20px 24px */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px; /* Reduced from 20px */
  transition: all 0.1s ease;
}

.hero-status-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

/* Auto-scanning animation */
.hero-status-card.auto-scanning {
  animation: autoScanPulse 1.5s ease-in-out infinite;
}

@keyframes autoScanPulse {
  0%, 100% {
    border-left-color: #23A094;
    background: #F0FDF4;
  }
  50% {
    border-left-color: #059669;
    background: #ECFDF5;
  }
}

.hero-content {
  display: flex;
  align-items: center;
  gap: 12px; /* Reduced from 16px */
  flex: 1;
  min-width: 0;
}

.hero-icon {
  font-size: 22px; /* Reduced from 28px */
  filter: drop-shadow(2px 2px 0px #000000);
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

.hero-text {
  flex: 1;
  min-width: 0;
}

.hero-title {
  font-size: 15px; /* Reduced from 18px */
  font-weight: 800;
  color: #242423;
  margin-bottom: 3px; /* Reduced from 4px */
  letter-spacing: -0.3px;
  word-wrap: break-word;
}

.hero-message {
  font-size: 12px; /* Reduced from 14px */
  font-weight: 600;
  color: #6F6F6F;
  line-height: 1.3;
  word-wrap: break-word;
}

.hero-action {
  flex-shrink: 0;
}

/* Groups Overview Section - Compact Optimized */
.groups-overview {
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  padding: 14px 18px; /* Reduced from 20px 24px */
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.groups-overview:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px; /* Reduced from 16px */
  gap: 12px; /* Reduced from 16px */
}

.overview-title {
  display: flex;
  align-items: center;
  gap: 10px; /* Reduced from 12px */
  flex: 1;
  min-width: 0;
}

.overview-title h3 {
  font-size: 14px; /* Reduced from 16px */
  font-weight: 800;
  color: #242423;
  letter-spacing: -0.2px;
  margin: 0;
  word-wrap: break-word;
}

.overview-actions {
  flex-shrink: 0;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px; /* Reduced from 12px */
}

.stat-item {
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 6px;
  box-shadow: 2px 2px 0px #000000;
  padding: 10px 12px; /* Reduced from 12px 16px */
  display: flex;
  align-items: center;
  gap: 8px; /* Reduced from 10px */
  transition: all 0.1s ease;
}

.stat-item:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.stat-icon {
  font-size: 16px; /* Reduced from 18px */
  filter: drop-shadow(1px 1px 0px #000000);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 14px; /* Reduced from 16px */
  font-weight: 800;
  color: #242423;
  line-height: 1;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 10px; /* Reduced from 11px */
  font-weight: 600;
  color: #6F6F6F;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

/* Group Selection Section */
.group-selection {
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.group-selection:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.selection-header {
  background: #F4F4F0;
  border-bottom: 3px solid #000000;
  border-radius: 8px 8px 0 0;
  padding: 12px 16px; /* Reduced from 16px 20px */
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px; /* Reduced from 16px */
}

.selection-header h4 {
  font-size: 14px; /* Reduced from 16px */
  font-weight: 800;
  color: #242423;
  letter-spacing: -0.2px;
  margin: 0;
  flex: 1;
  min-width: 0;
}

.selection-controls {
  display: flex;
  gap: 6px; /* Reduced from 8px */
  flex-shrink: 0;
}

/* Compact Search and Filter Toolbar - Optimized */
.search-filter-toolbar {
  background: #F4F4F0;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  border-radius: 4px;
  padding: 6px; /* Reduced from 8px */
  margin-bottom: 6px; /* Reduced from 8px */
  display: flex;
  gap: 4px; /* Reduced from 6px */
  align-items: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Search Input - Takes up available space - Compact */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.2s ease;
  height: 28px; /* Reduced from 32px */
  flex: 2;
  min-width: 120px; /* Reduced from 140px */
}

.search-input-wrapper:focus-within {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.search-icon {
  padding: 0 6px; /* Reduced from 8px */
  font-size: 12px; /* Reduced from 14px */
  color: #6B7280;
  pointer-events: none;
  display: flex;
  align-items: center;
  height: 100%;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 0 3px; /* Reduced from 4px */
  font-size: 11px; /* Reduced from 12px */
  font-weight: 600;
  background: transparent;
  color: #242423;
  height: 100%;
}

.search-input::placeholder {
  color: #6B7280;
  font-weight: 400;
}

.search-clear-btn {
  background: none;
  border: none;
  padding: 0 8px;
  cursor: pointer;
  color: #6B7280;
  font-weight: 800;
  font-size: 14px;
  transition: all 0.1s ease;
  height: 100%;
  display: flex;
  align-items: center;
}

.search-clear-btn:hover {
  color: #DC2626;
  transform: scale(1.1);
}

/* Compact Control Groups */
.control-group {
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.control-group.premium-feature {
  position: relative;
}

/* Compact Premium Overlays - Positioned at bottom-right corner to not obscure button content */
.premium-overlay {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 18px;
  height: 18px;
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 2;
  box-shadow: 2px 2px 0px #000000;
}

.premium-overlay:hover {
  background: #FF90E8;
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0px #000000;
}

.premium-overlay:hover .premium-badge {
  color: #000000;
  transform: scale(1.1);
}

.premium-badge {
  font-size: 10px;
  font-weight: 800;
  color: #FF90E8;
  transition: all 0.2s ease;
}



/* Enhanced Premium Feature Indicators - Subtle and Clean */
.premium-feature-indicator {
  display: inline-flex;
  align-items: center;
  background: #FF90E8;
  color: #000000;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  margin-left: 4px;
  border: 1px solid #000000;
  box-shadow: 1px 1px 0px #000000;
  transition: all 0.1s ease;
}

.premium-feature-indicator:hover {
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0px #000000;
}

.premium-feature-indicator.unlimited {
  background: #23A094;
  color: #FFFFFF;
  font-size: 8px;
}

/* Compact Button and Select Styles - Optimized */
.btn-sort.compact,
.btn-action.compact,
.filter-select.compact {
  height: 28px; /* Reduced from 32px */
  background: #FFFFFF;
  color: #242423;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  border-radius: 4px;
  font-size: 10px; /* Reduced from 11px */
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  white-space: nowrap;
  box-sizing: border-box;
}

/* Compact Sort Buttons */
.btn-sort.compact {
  padding: 0 8px; /* Reduced from 10px */
  gap: 3px; /* Reduced from 4px */
  min-width: 36px; /* Reduced from 40px */
}

.btn-sort.compact .btn-icon {
  font-size: 12px;
}

.btn-sort.compact .sort-indicator {
  font-size: 10px;
  font-weight: 800;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.btn-sort.compact.active .sort-indicator {
  opacity: 1;
}

.btn-sort.compact:hover:not(:disabled) {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.btn-sort.compact:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-sort.compact.active {
  background: #FF90E8;
  color: #000000;
}

/* Compact Filter Selects */
.filter-select.compact {
  padding: 0 24px 0 8px;
  flex: 1;
  min-width: 110px;
  font-weight: 600;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 6px center;
  background-size: 12px;
}

.filter-select.compact:hover:not(:disabled) {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.filter-select.compact:focus {
  outline: none;
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

/* Compact Action Button */
.btn-action.compact {
  padding: 0 10px;
  background: #E5E7EB;
  color: #374151;
  border-color: #6B7280;
  min-width: 40px;
}

.btn-action.compact .btn-icon {
  font-size: 12px;
}

.btn-action.compact:hover:not(:disabled) {
  background: #D1D5DB;
  color: #1F2937;
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

/* Responsive adjustments for compact toolbar */
@media (max-width: 600px) {
  .search-filter-toolbar {
    flex-wrap: wrap;
    gap: 4px;
    padding: 6px;
  }

  .search-input-wrapper {
    min-width: 100px;
    max-width: 150px;
  }

  .filter-select.compact {
    min-width: 80px;
    max-width: 100px;
    font-size: 10px;
  }

  .btn-sort.compact,
  .btn-action.compact {
    min-width: 28px;
    padding: 0 6px;
  }

  .btn-sort.compact .btn-icon,
  .btn-action.compact .btn-icon {
    font-size: 11px;
  }
}

/* Hide only on very small screens (actual mobile) */
@media (max-width: 480px) {
  .search-filter-toolbar {
    display: none; /* Hide on mobile as per requirements */
  }
}

.filter-status {
  background: #E5E7EB;
  border: 2px solid #6B7280;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.filter-status-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-status-count {
  color: #6B7280;
  font-weight: 400;
}

/* Empty groups message */
.empty-groups-message {
  text-align: center;
  padding: 40px 20px;
  color: #6B7280;
  grid-column: 1 / -1; /* Span all columns */
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 16px;
  font-weight: 700;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  font-weight: 400;
  color: #6B7280;
}

/* Group activity info for premium users */
.group-activity-info {
  font-size: 10px;
  font-weight: 600;
  color: #6B7280;
  background: #F3F4F6;
  border: 1px solid #D1D5DB;
  border-radius: 3px;
  padding: 2px 6px;
  margin-left: 6px;
  cursor: help;
  transition: all 0.1s ease;
}

.group-activity-info:hover {
  background: #E5E7EB;
  color: #374151;
}

.group-list-container {
  padding: 12px 16px; /* Reduced from 16px 20px */
  max-height: 280px; /* Reduced from 320px */
  overflow-x: hidden;
  overflow-y: auto;

  /* Ensure proper width calculation for grid layout */
  width: 100%;
  box-sizing: border-box;
}

/* Session Controls Section */
.session-controls {
  background: #F0FDF4;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #23A094;
  padding: 20px 24px;
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.session-controls:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.controls-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  gap: 16px;
}

.controls-header h4 {
  font-size: 16px;
  font-weight: 800;
  color: #242423;
  letter-spacing: -0.2px;
  margin: 0;
  flex: 1;
  min-width: 0;
}

.session-info {
  flex-shrink: 0;
}

.session-badge {
  background: #23A094;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 700;
  padding: 6px 12px;
  border-radius: 6px;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Compact Session Controls - Inline version for groups overview */
.session-controls-compact {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 2px solid #E5E7EB;
}

.control-buttons-inline {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* Button Variants for New Layout - Compact */
.btn-hero {
  background: #23A094;
  color: #FFFFFF;
  border-color: #000000;
  font-weight: 800;
  font-size: 14px; /* Reduced from 16px */
  padding: 12px 18px; /* Reduced from 16px 24px */
  min-height: 36px; /* Reduced from 48px */
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  position: relative;
  overflow: hidden;
}

.btn-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-hero:hover::before {
  left: 100%;
}

.btn-hero:hover:not(:disabled) {
  background: #1E8B7A;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-hero:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0px #000000;
  background: #1A7A6B;
}

.btn-hero .btn-icon {
  font-size: 20px;
  margin-right: 8px;
  transition: transform 0.2s ease;
}

.btn-hero:hover .btn-icon {
  transform: translateX(2px);
}

.btn-compact {
  padding: 8px 12px;
  min-height: 32px;
  font-size: 11px;
  font-weight: 700;
  border-radius: 6px;
  box-shadow: 2px 2px 0px #000000;
}

.btn-compact:hover:not(:disabled) {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.btn-compact:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0px #000000;
}

.btn-large {
  padding: 14px 20px;
  min-height: 44px;
  font-size: 14px;
  font-weight: 800;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  width: 100%;
}

.btn-large:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-large:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0px #000000;
}

/* Updated Progress Section */
.progress-section {
  background: #FFFBEB;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #F59E0B;
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.progress-section:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.progress-section .progress-header {
  background: #FFF8E1;
  border-bottom: 3px solid #000000;
  border-radius: 8px 8px 0 0;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.progress-section .progress-header h4 {
  font-size: 16px;
  font-weight: 800;
  color: #242423;
  letter-spacing: -0.2px;
  margin: 0;
  flex: 1;
  min-width: 0;
}

.progress-bar-container {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 12px;
  background: #E5E5E5;
  border-radius: 6px;
  overflow: hidden;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
}

.progress-fill {
  height: 100%;
  background: #F59E0B;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-percentage {
  font-size: 12px;
  font-weight: 800;
  color: #242423;
  min-width: 40px;
  text-align: right;
}

.progress-details {
  padding: 16px 20px;
  border-top: 2px solid #E5E5E5;
  background: #FFFFFF;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.progress-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 6px;
  box-shadow: 2px 2px 0px #000000;
  transition: all 0.1s ease;
}

.progress-item:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.progress-label {
  font-size: 11px;
  font-weight: 700;
  color: #242423;
  flex: 1;
}

.progress-value {
  font-size: 14px;
  font-weight: 800;
  color: #242423;
  min-width: 24px;
  text-align: right;
}

.current-action {
  padding: 16px 20px;
  border-top: 2px solid #E5E5E5;
  background: #FFF8E1;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  font-weight: 600;
  color: #6F6F6F;
  text-align: center;
  min-height: 20px;
}

/* Top Progress Indicator - Prominent loading/waiting feedback */
.top-progress-indicator {
  background: #FFFBEB;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #F59E0B;
  padding: 16px 20px;
  flex-shrink: 0;
  transition: all 0.1s ease;
  margin-bottom: 16px;
}

.top-progress-indicator:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.progress-indicator-content {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.progress-indicator-icon {
  font-size: 20px;
  filter: drop-shadow(2px 2px 0px #000000);
  flex-shrink: 0;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-indicator-text {
  flex: 1;
  font-size: 14px;
  font-weight: 700;
  color: #242423;
  letter-spacing: -0.2px;
}

.progress-indicator-bar {
  height: 8px;
  background: #E5E5E5;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid #000000;
  box-shadow: inset 2px 2px 0px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
}

.progress-indicator-fill {
  height: 100%;
  background: linear-gradient(90deg, #F59E0B 0%, #FBBF24 50%, #F59E0B 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-indicator-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.progress-indicator-actions .btn {
  font-size: 12px;
  padding: 8px 16px;
  min-height: 36px;
  font-weight: 700;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  transition: all 0.1s ease;
}

.progress-indicator-actions .btn:hover:not(:disabled) {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.progress-indicator-actions .btn:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0px #000000;
}

.progress-indicator-actions .btn-danger {
  background: #DC2626;
  color: #FFFFFF;
  border-color: #000000;
}

.progress-indicator-actions .btn-danger:hover:not(:disabled) {
  background: #B91C1C;
}

/* Debug Section */
.debug-section {
  flex-shrink: 0;
  text-align: center;
  padding: 8px 0;
}

/* Group Count Badge */
.group-count {
  font-size: 11px;
  color: #000000;
  background: #FFB3EC;
  padding: 6px 12px;
  border-radius: 6px;
  border: 2px solid #000000;
  font-weight: 800;
  box-shadow: 2px 2px 0px #000000;
  flex-shrink: 0;
  white-space: nowrap;
  transition: all 0.1s ease;
}

.group-count:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

/* Status Card - Desktop Design */
.status-card {
  display: flex;
  align-items: center;
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 3px 3px 0px #000000;
  border-left: 6px solid #FFB3EC;
  position: relative;
  flex-shrink: 0;
  padding: 12px 16px;
  margin-bottom: 0;
}

/* Connection Status Indicator */
.connection-status {
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 12px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 2px solid #000000;
  background: #FFFFFF;
  box-shadow: 2px 2px 0px #000000;
  transition: all 0.2s ease;
}

.connection-status.connected {
  background: #F0FDF4;
  border-color: #23A094;
}

.connection-status.disconnected {
  background: #FFFBEB;
  border-color: #F59E0B;
}

.status-card.status-success {
  background: #F0FDF4;
  border-left-color: #23A094;
}

.status-card.status-warning {
  background: #FFFBEB;
  border-left-color: #F59E0B;
}

.status-card.status-error {
  background: #FEF2F2;
  border-left-color: #DC2626;
}

.status-icon {
  /* Desktop icon size */
  font-size: 20px;
  margin-right: 10px;
  filter: drop-shadow(1px 1px 0px #000000);
  /* Prevent icon from shrinking */
  flex-shrink: 0;
}

.status-content {
  flex: 1;
  /* Ensure content doesn't overflow */
  min-width: 0;
}

.status-title {
  font-weight: 700;
  /* Desktop title size */
  font-size: 14px;
  margin-bottom: 3px;
  color: #242423;
  /* Handle text overflow */
  word-wrap: break-word;
}

.status-message {
  /* Desktop message size */
  font-size: 12px;
  font-weight: 500;
  color: #6F6F6F;
  /* Desktop line height */
  line-height: 1.3;
  /* Handle text overflow */
  word-wrap: break-word;
}

/* Page Detection */
.page-detection {
  background: #FFFBEB;
  border: 3px solid #F59E0B;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 3px 3px 0px #000000;
}

.detection-status {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.detection-icon {
  margin-right: 12px;
  font-size: 20px;
  filter: drop-shadow(1px 1px 0px #000000);
}

.page-url {
  font-size: 13px;
  color: #6F6F6F;
  word-break: break-all;
  font-weight: 500;
}

/* Navigation Helper - Desktop Design */
.navigation-helper {
  background: #F0FDF4;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #23A094;
  transition: all 0.1s ease;
  flex-shrink: 0;

  /* Desktop spacing - compact for efficiency */
  margin-top: 0;
  padding: 12px 14px;
}

.navigation-helper:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.navigation-message {
  display: flex;
  align-items: center;
  /* Desktop spacing */
  margin-bottom: 12px;
}

.navigation-icon {
  /* Desktop icon size */
  font-size: 20px;
  margin-right: 10px;
  filter: drop-shadow(2px 2px 0px #000000);
  animation: pulse 2s infinite;
  /* Prevent icon from shrinking */
  flex-shrink: 0;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.navigation-content {
  flex: 1;
  /* Ensure content doesn't overflow */
  min-width: 0;
}

.navigation-title {
  /* Desktop title size */
  font-size: 14px;
  font-weight: 800;
  color: #242423;
  margin-bottom: 3px;
  letter-spacing: -0.2px;
  /* Handle text overflow */
  word-wrap: break-word;
}

.navigation-subtitle {
  /* Desktop subtitle */
  font-size: 12px;
  font-weight: 600;
  color: #23A094;
  line-height: 1.3;
  /* Handle text overflow */
  word-wrap: break-word;
}

/* Group Preview - Desktop Design */
.group-preview {
  border: 3px solid #000000;
  border-radius: 4px;
  background: #FFFFFF;
  box-shadow: 3px 3px 0px #000000;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-bottom: 0;
  height: 100%;
  min-height: 380px; /* Increased for better content fit */
  flex: 1;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px; /* Reduced from 10px 12px */
  background: #F4F4F0;
  border-bottom: 3px solid #000000;
  border-radius: 4px 4px 0 0;
  flex-shrink: 0;
}

.preview-header h3 {
  font-size: 12px; /* Reduced from 14px */
  font-weight: 700;
  color: #242423;
  letter-spacing: -0.2px;
  word-wrap: break-word;
}

.group-count {
  font-size: 11px;
  color: #000000;
  background: #FFB3EC;
  padding: 4px 8px;
  border-radius: 4px;
  border: 2px solid #000000;
  font-weight: 700;
  box-shadow: 2px 2px 0px #000000;
  flex-shrink: 0;
  white-space: nowrap;
}

.selection-controls {
  display: flex;
  gap: 6px;
  padding: 8px 12px;
  border-bottom: 3px solid #000000;
  background: #F4F4F0;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.debug-actions {
  /* Compact padding for popup */
  padding: 8px 12px;
  border-top: 3px solid #E5E5E5;
  background: #F4F4F0;
  /* Prevent shrinking */
  flex-shrink: 0;
}

.group-list {
  flex: 1;
  overflow-x: hidden; /* Remove horizontal scrolling only */
  overflow-y: auto; /* Allow vertical scrolling for group list */
  background: #FFFFFF;
  padding: 8px 12px;
  min-height: 280px; /* Better fit for content */
  max-height: 400px; /* Increased max height for better content display */

  /* 3-column grid layout for optimal organization
     - 30 items per load = 10 perfect rows
     - Consistent visual structure
     - Maximum information density for desktop/tablet (800px+) */
  display: grid;
  gap: 6px; /* Reduced from 8px for more compact layout */
  align-content: start;

  /* Ensure equal column distribution and prevent content overflow */
  grid-auto-rows: min-content;
  width: 100%;
  box-sizing: border-box;

  /* Force equal column widths regardless of content */
  grid-template-columns: repeat(3, minmax(0, 1fr));
  justify-items: stretch;
}

.group-item {
  display: flex;
  align-items: center;
  border-radius: 4px;
  transition: all 0.1s ease;
  border: 3px solid #000000;
  background: #F4F4F0;
  box-shadow: 3px 3px 0px #000000;
  /* Better interaction */
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
  user-select: none;

  /* Desktop - optimized for 3-column grid layout - Compact */
  padding: 8px; /* Reduced from 12px */
  min-height: 36px; /* Reduced from 44px */
  margin-bottom: 0; /* Remove margin since grid gap handles spacing */

  /* Ensure proper sizing in 3-column grid */
  width: 100%;
  box-sizing: border-box;

  /* Prevent content overflow that could affect column widths */
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.group-item.disabled {
  background: #F8F8F8;
  opacity: 0.6;
  cursor: not-allowed;
}

.group-item.disabled .group-name {
  color: #999;
}

.group-item.disabled .group-checkbox {
  cursor: not-allowed;
}

.group-item.disabled .group-checkmark {
  cursor: not-allowed;
  opacity: 0.5;
}

.group-item:hover:not(.disabled) {
  background: #FFFFFF;
  border-color: #000000;
  transform: translate(-2px, -2px);
  box-shadow: 5px 5px 0px #000000;
}

.group-item.disabled:hover {
  background: #F8F8F8;
  border-color: #000000;
  transform: none;
  box-shadow: 3px 3px 0px #000000;
}

/* Hide native checkbox for group items */
.group-checkbox {
  display: none;
}

/* Custom checkbox styling for group items - Desktop Design - Compact */
.group-checkmark {
  border: 2px solid #000000;
  border-radius: 4px;
  position: relative;
  transition: all 0.1s ease;
  background: #FFFFFF;
  box-shadow: 1px 1px 0px #000000;
  flex-shrink: 0;
  cursor: pointer;
  /* Better interaction */
  -webkit-tap-highlight-color: transparent;

  /* Desktop - compact checkboxes for higher density */
  width: 14px; /* Reduced from 18px */
  height: 14px; /* Reduced from 18px */
  margin-right: 6px; /* Reduced from 8px */
}

.group-checkbox:checked + .group-checkmark {
  background: #FF90E8;
  border-color: #000000;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

/* Selected group item styling for better visual feedback */
.group-item.selected {
  background: #FFF0FC;
  border-color: #FF90E8;
  box-shadow: 4px 4px 0px #FF90E8;
}

.group-item.selected:hover:not(.disabled) {
  background: #FFE8F7;
  border-color: #FF90E8;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #FF90E8;
}

.group-checkbox:checked + .group-checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000000;
  /* Compact checkmark for popup */
  font-size: 14px;
  font-weight: 900;
  filter: drop-shadow(1px 1px 0px #FFFFFF);
}

.group-name {
  flex: 1;
  font-weight: 700;
  color: #242423;
  cursor: pointer;
  user-select: none; /* Prevent text selection when clicking */
  line-height: 1.2;
  /* Handle text overflow in compact grid layout */
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* Ensure content doesn't overflow and affect column widths */
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Desktop text size - optimized for 3-column readability - Compact */
  font-size: 12px; /* Reduced from 14px */
}

.group-status {
  font-weight: 800;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  cursor: pointer; /* Prevent clicking on badges from toggling checkbox */

  /* Desktop - compact status badges optimized for grid layout - Extra Compact */
  font-size: 8px; /* Reduced from 9px */
  padding: 1px 4px; /* Reduced from 2px 5px */
  border-radius: 3px;
  margin-left: 4px; /* Reduced from 6px */
  transition: all 0.1s ease;

  /* Ensure consistent badge sizing */
  min-width: 28px; /* Reduced from 32px */
  text-align: center;
  white-space: nowrap;
}

.group-status:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.group-status.admin {
  background: #DC2626;
  color: #FFFFFF;
  border-color: #000000;
}

.group-status.active {
  background: #23A094;
  color: #FFFFFF;
  border-color: #000000;
}

.group-status.left {
  background: #16A34A;
  color: #FFFFFF;
  border-color: #000000;
}

.group-item.left-group {
  opacity: 0.6;
  background: #F9FBF9;
  border-color: #16A34A;
}

.group-item.left-group .group-name {
  text-decoration: line-through;
  color: #6B7280;
}



/* Progress Section - Compact */
.progress-section {
  border: 3px solid #000000;
  border-radius: 4px;
  margin-bottom: 16px; /* Reduced from 20px */
  background: #FFFFFF;
  box-shadow: 4px 4px 0px #000000;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px; /* Reduced from 16px 20px */
  background: #F4F4F0;
  border-bottom: 3px solid #000000;
  border-radius: 4px 4px 0 0;
}

.progress-header h3 {
  font-size: 15px; /* Reduced from 18px */
  font-weight: 700;
  color: #242423;
}

.progress-stats {
  font-size: 13px; /* Reduced from 15px */
  color: #6F6F6F;
  font-weight: 600;
}

.progress-bar {
  height: 12px;
  background: #E5E5E5;
  margin: 20px;
  border-radius: 4px;
  overflow: hidden;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
}

.progress-fill {
  height: 100%;
  background: #FF90E8;
  width: 0%;
  transition: width 0.3s ease;
  border-right: 2px solid #000000;
}

.progress-details {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  background: #F4F4F0;
}

.progress-item {
  text-align: center;
}

.progress-label {
  display: block;
  font-size: 12px;
  color: #6F6F6F;
  margin-bottom: 4px;
}

.progress-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #242423;
}

.current-action {
  padding: 12px 16px;
  font-size: 14px;
  color: #6F6F6F;
  border-top: 1px solid #E5E5E5;
  background: #FFFFFF;
  border-radius: 0 0 4px 4px;
}

/* Enhanced UX - Delay Controls */
.delay-controls {
  border: 3px solid #000000;
  border-radius: 4px;
  margin-bottom: 20px;
  background: #FFF8E1;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #F59E0B;
  display: none;
}

.delay-info {
  padding: 16px 20px;
  font-size: 15px;
  font-weight: 600;
  color: #242423;
  border-bottom: 2px solid #000000;
  background: #FFFBEB;
}

.delay-progress-container {
  padding: 12px 20px;
  background: #FFF8E1;
}

.delay-progress {
  height: 8px;
  background: #F59E0B;
  border-radius: 4px;
  transition: width 0.3s ease;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
  width: 0%;
}

.delay-buttons {
  display: flex;
  gap: 10px; /* Reduced from 12px */
  padding: 12px 16px; /* Reduced from 16px 20px */
  background: #FFF8E1;
  border-top: 2px solid #000000;
  flex-wrap: wrap;
}

.btn-small {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 700;
  border: 2px solid #000000;
  border-radius: 4px;
  box-shadow: 3px 3px 0px #000000;
  background: #FFFFFF;
  color: #242423;
  cursor: pointer;
  transition: all 0.1s ease;
}

.btn-small:hover {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

.btn-small:active {
  transform: translate(1px, 1px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-small.active {
  background: #FF90E8;
  color: #000000;
}

.skip-delay-btn {
  background: #23A094;
  color: #FFFFFF;
}

.continue-manual-btn {
  background: #4267B2;
  color: #FFFFFF;
}

.toggle-manual-btn {
  background: #F59E0B;
  color: #FFFFFF;
}

/* Buttons - Desktop Design - Compact Optimized */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px; /* Reduced from 10px 16px */
  min-height: 28px; /* Reduced from 36px */
  border: 3px solid #000000;
  border-radius: 4px;
  font-size: 11px; /* Reduced from 12px */
  font-weight: 700;
  cursor: pointer;
  transition: all 0.1s ease;
  text-decoration: none;
  gap: 4px; /* Reduced from 6px */
  line-height: 1.2;
  box-shadow: 3px 3px 0px #000000;
  position: relative;
  -webkit-tap-highlight-color: transparent;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #CCCCCC !important;
  color: #6F6F6F !important;
  border-color: #CCCCCC !important;
  box-shadow: 1px 1px 0px #CCCCCC !important; /* Reduced shadow */
}

.btn-primary {
  background: #FF90E8;
  color: #000000;
  border-color: #000000;
}

.btn-primary:hover:not(:disabled) {
  background: #FF90E8;
  transform: translate(-1px, -1px); /* Reduced transform */
  box-shadow: 3px 3px 0px #000000; /* Reduced shadow */
}

.btn-primary:active:not(:disabled) {
  transform: translate(1px, 1px); /* Reduced transform */
  box-shadow: 1px 1px 0px #000000; /* Reduced shadow */
}

.btn-secondary {
  background: #FFFFFF;
  color: #242423;
  border-color: #000000;
}

.btn-secondary:hover:not(:disabled) {
  background: #F4F4F0;
  transform: translate(-1px, -1px); /* Reduced transform */
  box-shadow: 3px 3px 0px #000000; /* Reduced shadow */
}

.btn-secondary:active:not(:disabled) {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-warning {
  background: #F59E0B;
  color: #000000;
  border-color: #000000;
}

.btn-warning:hover:not(:disabled) {
  background: #F59E0B;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-warning:active:not(:disabled) {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-success {
  background: #23A094;
  color: #FFFFFF;
  border-color: #000000;
}

.btn-success:hover:not(:disabled) {
  background: #23A094;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-success:active:not(:disabled) {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-danger {
  background: #DC2626;
  color: #FFFFFF;
  border-color: #000000;
}

.btn-danger:hover:not(:disabled) {
  background: #DC2626;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-danger:active:not(:disabled) {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0px #000000;
}

.btn-small {
  /* Desktop small buttons - Extra Compact */
  padding: 6px 10px; /* Reduced from 8px 12px */
  min-height: 24px; /* Reduced from 28px */
  font-size: 10px; /* Reduced from 11px */
  font-weight: 700;
  box-shadow: 3px 3px 0px #000000;
  /* Better interaction */
  -webkit-tap-highlight-color: transparent;
}

.btn-small:hover:not(:disabled) {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

.btn-small:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0px #000000;
}

.btn-navigation {
  background: #23A094;
  color: #FFFFFF;
  border-color: #000000;
  font-weight: 800;
  width: 100%;
  margin-top: 0;
  /* Desktop navigation button */
  font-size: 14px;
  padding: 14px 18px;
  min-height: 42px;
  position: relative;
  overflow: hidden;
  /* Better interaction */
  -webkit-tap-highlight-color: transparent;
}

.btn-navigation::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-navigation:hover::before {
  left: 100%;
}

.btn-navigation:hover:not(:disabled) {
  background: #1E8B7A;
  transform: translate(-3px, -3px);
  box-shadow: 7px 7px 0px #000000;
  border-color: #000000;
}

.btn-navigation:active:not(:disabled) {
  transform: translate(1px, 1px);
  box-shadow: 3px 3px 0px #000000;
  background: #1A7A6B;
}

.btn-navigation .btn-icon {
  font-size: 18px;
  margin-right: 8px;
  transition: transform 0.2s ease;
}

.btn-navigation:hover .btn-icon {
  transform: translateX(2px);
}

.btn-navigation:disabled {
  background: #CCCCCC !important;
  color: #6F6F6F !important;
  border-color: #CCCCCC !important;
  box-shadow: 2px 2px 0px #CCCCCC !important;
  cursor: not-allowed;
  transform: none !important;
}

.btn-navigation:disabled::before {
  display: none;
}

.btn-navigation:disabled .btn-icon {
  transform: none !important;
}

.btn-icon {
  font-size: 16px;
  font-weight: 700;
}

/* Action Buttons - Desktop Design - Extra Compact */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px; /* Reduced from 6px */
  flex-shrink: 0;
  margin-top: 0;
}

.action-buttons .btn {
  width: 100%;
  min-height: 24px; /* Reduced from 28px */
  font-size: 9px; /* Reduced from 10px */
  font-weight: 700;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: #6F6F6F;
}

/* Settings Styles - Desktop Design */
.settings-left-column,
.settings-right-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.settings-section {
  margin-bottom: 0;
  padding: 10px 12px; /* Reduced from 14px 16px */
  background: #FFFFFF;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 3px 3px 0px #000000;
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.settings-section:hover {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

/* Section Headers - Desktop Design - Compact */
.section-header {
  /* Desktop spacing */
  margin-bottom: 8px; /* Reduced from 12px */
}

.section-header h3 {
  font-size: 14px; /* Reduced from 16px */
  font-weight: 700;
  margin-bottom: 3px; /* Reduced from 4px */
  color: #242423;
  letter-spacing: -0.3px;
  text-transform: uppercase;
  filter: drop-shadow(1px 1px 0px #F4F4F0);
  word-wrap: break-word;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 11px; /* Reduced from 13px */
  font-weight: 600;
  color: #6F6F6F;
  line-height: 1.4;
  word-wrap: break-word;
}

/* Essential Settings - IMPROVED Header */
.essential-settings-section .section-header {
  margin-bottom: 8px; /* Increased spacing for better hierarchy */
}

.essential-settings-section .section-header h3 {
  font-size: 15px; /* Slightly larger for better readability */
  margin-bottom: 2px; /* Small spacing for visual separation */
  line-height: 1.2; /* Improved line height for readability */
}

.essential-settings-section .section-subtitle {
  font-size: 12px; /* Increased for better readability */
  line-height: 1.3; /* Improved line height */
}







.btn-premium {
  background: #000000;
  color: #FFFFFF;
  border-color: #000000;
  font-weight: 800;
  width: 100%;
}

.btn-premium:hover:not(:disabled) {
  background: #242423;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.btn-success {
  background: #23A094;
  color: #FFFFFF;
  border-color: #000000;
  font-weight: 800;
}

.btn-success:hover:not(:disabled) {
  background: #1E8A7A;
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}



.setting-item {
  /* Desktop spacing */
  margin-bottom: 10px;
}

/* Essential Settings - IMPROVED Setting Items */
.essential-settings-section .setting-item {
  margin-bottom: 4px; /* Small margin for visual separation */
}

/* Setting Headers - Desktop Design */
.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* Desktop spacing */
  margin-bottom: 6px;
  /* Allow wrapping */
  flex-wrap: wrap;
  gap: 6px;
}

.setting-header label {
  /* Desktop text size */
  font-size: 13px;
  font-weight: 700;
  color: #242423;
  margin: 0;
  /* Handle text overflow */
  word-wrap: break-word;
}

.setting-value-display {
  /* Desktop badge */
  font-size: 11px;
  font-weight: 800;
  color: #FF90E8;
  background: #F4F4F0;
  padding: 3px 8px;
  border-radius: 4px;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
  /* Prevent shrinking */
  flex-shrink: 0;
  white-space: nowrap;
}

/* Range Slider */
.range-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #E5E5E5;
  outline: none;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
  -webkit-appearance: none;
  margin: 12px 0;
}

.range-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #FF90E8;
  border: 3px solid #000000;
  cursor: pointer;
  box-shadow: 2px 2px 0px #000000;
  transition: all 0.1s ease;
}

.range-slider::-webkit-slider-thumb:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.range-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #FF90E8;
  border: 3px solid #000000;
  cursor: pointer;
  box-shadow: 2px 2px 0px #000000;
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  font-weight: 700;
  color: #6F6F6F;
  margin-top: -8px;
  margin-bottom: 8px;
}

.setting-item label {
  display: block;
  font-weight: 600;
  margin-bottom: 6px;
  color: #242423;
  font-size: 15px;
}

.setting-item input[type="number"] {
  width: 100%;
  padding: 12px 16px;
  border: 3px solid #000000;
  border-radius: 4px;
  font-size: 14px;
  background: #FFFFFF;
  transition: all 0.1s ease;
  font-weight: 600;
  box-shadow: 2px 2px 0px #000000;
}

.setting-item input[type="number"]:focus {
  outline: none;
  border-color: #FF90E8;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.setting-item select {
  width: 100%;
  padding: 12px 16px;
  border: 3px solid #000000;
  border-radius: 4px;
  background: #FFFFFF;
  font-size: 14px;
  font-weight: 600;
  color: #242423;
  cursor: pointer;
  box-shadow: 3px 3px 0px #000000;
  transition: all 0.1s ease;
}

.setting-item select:hover {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

.setting-item select:focus {
  outline: none;
  border-color: #FF90E8;
  box-shadow: 4px 4px 0px #FF90E8;
}

.setting-item input:disabled {
  background: #F4F4F0;
  color: #6F6F6F;
  cursor: not-allowed;
  opacity: 0.7;
}

.setting-item small {
  display: block;
  margin-top: 6px;
  font-size: 13px;
  color: #6F6F6F;
  font-weight: 500;
}

/* Enhanced Checkbox Styles - Desktop Design - Compact */
.checkbox-label {
  display: flex !important;
  align-items: flex-start;
  cursor: pointer;
  margin-bottom: 0 !important;
  padding: 6px 8px; /* Reduced from 10px */
  border-radius: 4px;
  transition: all 0.1s ease;
  border: 2px solid transparent;
}

/* Essential Settings - IMPROVED Checkbox Labels */
.essential-settings-section .checkbox-label {
  padding: 6px 8px; /* Reduced from 8px 10px */
  align-items: flex-start; /* Align to top for better text layout */
}

.checkbox-label:hover {
  background: #F4F4F0;
  border-color: #000000;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #000000;
  border-radius: 4px;
  margin-right: 12px;
  margin-top: 2px;
  position: relative;
  transition: all 0.1s ease;
  background: #FFFFFF;
  box-shadow: 2px 2px 0px #000000;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #FF90E8;
  border-color: #000000;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000000;
  font-size: 14px;
  font-weight: 900;
  filter: drop-shadow(1px 1px 0px #FFFFFF);
}

.checkbox-content {
  flex: 1;
}

.checkbox-title {
  font-size: 14px;
  font-weight: 700;
  color: #242423;
  margin-bottom: 2px;
  line-height: 1.3;
}

.checkbox-description {
  font-size: 12px;
  font-weight: 500;
  color: #6F6F6F;
  line-height: 1.3;
}

/* Essential Settings - IMPROVED Checkbox Content */
.essential-settings-section .checkbox-title {
  font-size: 13px; /* Readable title size */
  font-weight: 700;
  margin-bottom: 2px; /* Small spacing for visual separation */
  line-height: 1.3; /* Improved line height for readability */
}

.essential-settings-section .checkbox-description {
  font-size: 11px; /* Readable description size */
  font-weight: 500;
  line-height: 1.4; /* Improved line height for readability */
}

/* Premium Features Section */
.premium-features-section {
  border-color: #FF90E8;
  background: linear-gradient(135deg, #FFF8E1 0%, #FFFFFF 100%);
  position: relative;
}

.premium-features-section::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #FF90E8, #4267B2, #23A094);
  border-radius: 8px;
  z-index: -1;
  opacity: 0.2;
}

.premium-feature {
  position: relative;
}

/* Enhanced styling for disabled premium features */
.premium-disabled {
  opacity: 0.8;
  cursor: pointer; /* Changed from not-allowed to encourage clicking */
  position: relative;
}

.premium-disabled:hover {
  opacity: 1;
  background: rgba(255, 144, 232, 0.1) !important;
  border-color: #FF90E8 !important;
  transform: translate(-1px, -1px) !important;
  box-shadow: 3px 3px 0px rgba(255, 144, 232, 0.3) !important;
}

/* Add subtle premium indicator to disabled features */
.premium-disabled::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed rgba(255, 144, 232, 0.4);
  border-radius: 6px;
  pointer-events: none;
  z-index: 1;
}

.premium-disabled .checkmark {
  background: #E5E5E5 !important;
  border-color: #CCCCCC !important;
  cursor: not-allowed;
}

.premium-disabled input[type="checkbox"]:checked + .checkmark {
  background: #CCCCCC !important;
  border-color: #999999 !important;
}

.premium-indicator {
  display: inline-block;
  background: #FF90E8;
  color: #000000;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 8px;
  border: 2px solid #000000;
  box-shadow: 2px 2px 0px #000000;
}

/* Premium Upgrade Notice - Optimized for 430x600 popup */
.premium-upgrade-notice {
  margin-top: 16px;
  padding: 16px;
  background: #FF90E8;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
}

.upgrade-message {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upgrade-icon {
  font-size: 28px;
  filter: drop-shadow(2px 2px 0px #000000);
}

.upgrade-text {
  flex: 1;
}

.upgrade-title {
  font-size: 16px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 2px;
  letter-spacing: -0.2px;
  line-height: 1.3;
}

.upgrade-subtitle {
  font-size: 12px;
  font-weight: 600;
  color: #242423;
  line-height: 1.3;
}

/* Settings actions removed - using auto-save now */

/* History Styles - Desktop Design */
.history-left-column,
.history-right-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
}

.history-left-column {
  flex: 1.2; /* Give more space to history list */
}

.history-right-column {
  flex: 0.8; /* Less space for stats */
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding: 14px 16px;
  background: linear-gradient(135deg, #F0FDF4 0%, #F4F4F0 100%);
  border-radius: 4px;
  border: 3px solid #23A094;
  box-shadow: 4px 4px 0px #000000;
  transition: all 0.1s ease;
}

.history-header:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.history-header h3 {
  font-size: 16px;
  font-weight: 800;
  color: #242423;
  text-transform: uppercase;
  letter-spacing: -0.3px;
  filter: drop-shadow(1px 1px 0px #FFFFFF);
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-header h3::before {
  content: '📊';
  font-size: 18px;
}

/* History List Styling */
.history-list {
  background: #FFFFFF;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  padding: 16px;
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}

.history-empty {
  text-align: center;
  color: #6F6F6F;
  font-style: italic;
  padding: 40px 20px;
  font-size: 14px;
}

.history-empty::before {
  content: '📝';
  display: block;
  font-size: 32px;
  margin-bottom: 12px;
}

/* Statistics Section */
.stats-section,
.recent-activity-section {
  background: #FFFFFF;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  padding: 18px 20px;
  transition: all 0.1s ease;
}

.stats-section:hover,
.recent-activity-section:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 16px;
}

/* Activity Timeline Styling */
.activity-timeline {
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 12px 0;
}

.timeline-empty {
  text-align: center;
  color: #6F6F6F;
  font-style: italic;
  padding: 40px 20px;
  font-size: 14px;
}

.timeline-empty::before {
  content: '⏰';
  display: block;
  font-size: 32px;
  margin-bottom: 12px;
}

.stat-card {
  display: flex;
  align-items: center;
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 3px 3px 0px #000000;
  padding: 12px 14px;
  transition: all 0.1s ease;
}

.stat-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
  background: #FFFFFF;
}

.stat-icon {
  font-size: 18px;
  margin-right: 10px;
  filter: drop-shadow(1px 1px 0px #000000);
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #242423;
  letter-spacing: -0.2px;
  line-height: 1.1;
}

.stat-label {
  font-size: 11px;
  font-weight: 600;
  color: #6F6F6F;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-top: 2px;
}

.history-list {
  max-height: 320px; /* Better height for content */
  overflow: hidden; /* Remove scrolling */
  background: #FFFFFF;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 3px 3px 0px #000000;
  padding: 12px;
}

.history-empty {
  text-align: center;
  color: #6F6F6F;
  padding: 60px 30px;
  font-style: italic;
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  font-size: 16px;
  font-weight: 600;
}

.history-item {
  padding: 16px 18px;
  border: 3px solid #000000;
  border-radius: 4px;
  margin-bottom: 12px;
  background: #F4F4F0;
  transition: all 0.1s ease;
  box-shadow: 3px 3px 0px #000000;
  cursor: pointer;
}

.history-item:hover {
  background: #FFFFFF;
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

.history-date {
  font-size: 12px;
  color: #6F6F6F;
  margin-bottom: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-summary {
  font-size: 16px;
  font-weight: 800;
  color: #242423;
  margin-bottom: 4px;
  letter-spacing: -0.2px;
}

.history-details {
  font-size: 13px;
  color: #6F6F6F;
  margin-top: 6px;
  font-weight: 600;
}

/* Activity Timeline */
.activity-timeline {
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.timeline-empty {
  text-align: center;
  color: #6F6F6F;
  padding: 40px 20px;
  font-style: italic;
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 3px 3px 0px #000000;
  font-size: 14px;
  font-weight: 600;
}

.timeline-item {
  display: flex;
  align-items: center;
  padding: 12px 14px;
  border-radius: 4px;
  border: 2px solid #000000;
  background: #F4F4F0;
  margin-bottom: 8px;
  transition: all 0.1s ease;
  box-shadow: 2px 2px 0px #000000;
}

.timeline-item:hover {
  background: #FFFFFF;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

.timeline-icon {
  font-size: 16px;
  margin-right: 10px;
  filter: drop-shadow(1px 1px 0px #000000);
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
  min-width: 0;
}

.timeline-title {
  font-size: 13px;
  font-weight: 700;
  color: #242423;
  margin-bottom: 2px;
}

.timeline-time {
  font-size: 11px;
  font-weight: 600;
  color: #6F6F6F;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Neubrutalism Scrollbar Styling - Gumroad Design System */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 14px;
  height: 14px;
}

::-webkit-scrollbar-track {
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 4px;
  box-shadow: inset 2px 2px 0px #E5E5E5;
}

::-webkit-scrollbar-thumb {
  background: #6B7280;
  border: 3px solid #000000;
  border-radius: 4px;
  box-shadow: 2px 2px 0px #000000;
  transition: all 0.1s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #4B5563;
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
}

::-webkit-scrollbar-thumb:active {
  background: #374151;
  transform: translate(1px, 1px);
  box-shadow: 1px 1px 0px #000000;
}

::-webkit-scrollbar-corner {
  background: #F4F4F0;
  border: 2px solid #000000;
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: #6B7280 #F4F4F0;
}

/* Desktop Selection Controls - Already defined above, removing duplicate */

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .navigation-icon {
    animation: none;
  }
}

/* Focus Styles for Keyboard Navigation */
.btn:focus,
.group-item:focus {
  outline: 3px solid #FF90E8;
  outline-offset: 2px;
}

/* Tab button focus - only when not active to avoid conflicts */
.tab-button:focus:not(.active) {
  outline: 3px solid #FF90E8;
  outline-offset: 2px;
}

/* Free Tier Upgrade Prompt - Appears after group selection */
.free-tier-upgrade-prompt {
  background: linear-gradient(135deg, #FFF7ED 0%, #FFFBEB 100%);
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #FF90E8;
  padding: 20px 24px;
  flex-shrink: 0;
  transition: all 0.1s ease;
  margin-top: 16px;
}

.free-tier-upgrade-prompt:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

/* Selection Limit Upgrade Prompt - Appears when 3-group limit reached */
.selection-limit-upgrade-prompt {
  background: linear-gradient(135deg, #FEF3F2 0%, #FFFBFA 100%);
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #DC2626;
  padding: 14px;
  flex-shrink: 0;
  transition: all 0.1s ease;
  margin-top: 12px;
}

.selection-limit-upgrade-prompt:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

/* Tab Switch Upgrade Prompt - Appears when user tries to switch tabs during processing */
.tab-switch-upgrade-prompt {
  background: linear-gradient(135deg, #F0F9FF 0%, #F8FAFC 100%);
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  border-left: 6px solid #0EA5E9;
  padding: 14px;
  flex-shrink: 0;
  transition: all 0.1s ease;
  margin-top: 12px;
}

.tab-switch-upgrade-prompt:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}



.upgrade-prompt-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.upgrade-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
  filter: drop-shadow(1px 1px 0px #000000);
}

.upgrade-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 800;
  color: #000000;
  letter-spacing: -0.3px;
}

.upgrade-content p {
  margin: 0;
  color: #6B7280;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.5;
}

.upgrade-prompt-actions {
  margin-bottom: 16px;
}

.upgrade-benefits-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Compact upgrade benefits for smaller prompts */
.upgrade-benefits-compact {
  text-align: center;
  margin-top: 8px;
}

.upgrade-benefits-compact .benefit-text {
  font-size: 11px;
  font-weight: 600;
  color: #6B7280;
  line-height: 1.3;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: #6B7280;
  padding: 4px 0;
}

.benefit-icon {
  font-size: 14px;
  flex-shrink: 0;
  filter: drop-shadow(1px 1px 0px #000000);
}

.benefit-text {
  font-weight: 600;
  line-height: 1.3;
}

/* Feature Comparison Styles - Neubrutalism Design */
.comparison-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 4px;
  box-shadow: 4px 4px 0px #000000;
}

.comparison-header h2 {
  font-size: 24px;
  font-weight: 800;
  color: #000000;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.comparison-subtitle {
  font-size: 14px;
  font-weight: 600;
  color: #6F6F6F;
  margin: 0;
}

.comparison-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 0; /* Remove excessive bottom margin */
  /* Remove min-height constraint to allow natural content flow */
}

/* Tier Column Styling */
.tier-column {
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 4px;
  box-shadow: 4px 4px 0px #000000;
  overflow: visible; /* Allow content to expand */
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  /* Remove height constraint to allow natural content flow */
}

.tier-column:hover {
  transform: translate(-2px, -2px);
  box-shadow: 6px 6px 0px #000000;
}

.tier-free {
  border-color: #6F6F6F;
}

.tier-premium {
  border-color: #FF90E8;
  position: relative;
}



/* Tier Header */
.tier-header {
  padding: 20px 16px 16px 16px;
  background: linear-gradient(135deg, #F8F8F8 0%, #FFFFFF 100%);
  border-bottom: 3px solid #000000;
  text-align: center;
}

.tier-premium .tier-header {
  background: linear-gradient(135deg, #FF90E8 0%, #FFB3EC 100%);
}

.tier-badge {
  display: inline-block;
  padding: 4px 12px;
  border: 2px solid #000000;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 800;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  box-shadow: 2px 2px 0px #000000;
}

.tier-badge-free {
  background: #E5E5E5;
  color: #000000;
}

.tier-badge-premium {
  background: #000000;
  color: #FFFFFF;
}

.tier-title {
  font-size: 18px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 8px;
  letter-spacing: -0.3px;
}

.tier-price {
  font-size: 28px;
  font-weight: 800;
  color: #000000;
  margin-bottom: 4px;
  letter-spacing: -0.5px;
}

.tier-period {
  font-size: 14px;
  font-weight: 600;
  color: #6F6F6F;
}

.tier-description {
  font-size: 12px;
  font-weight: 600;
  color: #6F6F6F;
  margin: 0;
  line-height: 1.4;
}

.no-subscription {
  font-size: 11px;
  font-weight: 500;
  color: #23A094;
  font-style: italic;
}

/* Feature List */
.tier-features {
  padding: 16px;
  flex: 1; /* Fill available space */
}

/* Feature Section Headers */
.feature-section-header {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #E5E5E5;
}

.feature-section-title {
  font-size: 14px;
  font-weight: 800;
  color: #000000;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px; /* Reduced margin for tighter spacing */
  padding: 6px 8px; /* Slightly reduced padding */
  border-radius: 4px;
  transition: all 0.1s ease;
}

.feature-item:hover {
  background: #F8F8F8;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  font-size: 16px;
  font-weight: 800;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
  filter: drop-shadow(1px 1px 0px #000000);
}

.feature-included .feature-icon {
  color: #23A094;
}

.feature-limited .feature-icon {
  color: #FF6B35;
}

.feature-premium .feature-icon {
  color: #FF90E8;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 13px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 2px;
  line-height: 1.3;
}

.feature-description {
  font-size: 11px;
  font-weight: 500;
  color: #6F6F6F;
  line-height: 1.3;
}

/* Tier CTA */
.tier-cta {
  padding: 16px;
  border-top: 3px solid #000000;
  background: #F8F8F8;
}

.tier-premium .tier-cta {
  background: linear-gradient(135deg, #FF90E8 0%, #FFB3EC 100%);
}

.btn-tier {
  width: 100%;
  font-size: 14px;
  font-weight: 800;
  padding: 12px 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Essential Settings Section - IMPROVED Layout - Compact */
.essential-settings-section {
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 4px;
  box-shadow: 4px 4px 0px #000000;
  padding: 10px 12px; /* Reduced from 12px 16px */
  margin-bottom: 0; /* Remove bottom margin to reduce excessive spacing */
}

.essential-settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px; /* Reduced from 8px */
  margin-bottom: 6px; /* Reduced from 8px */
}

/* Enhanced History Tab Styles - Neubrutalism Design */
/* Only target History tab specific elements to avoid affecting Main tab */

/* History Container */
#history-tab .history-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

/* History Header - Compact */
#history-tab .history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px; /* Reduced from 18px 20px */
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #23A094;
  box-shadow: 4px 4px 0px #000000;
  transition: all 0.1s ease;
}

#history-tab .history-header:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

#history-tab .header-content h3 {
  font-size: 18px;
  font-weight: 800;
  color: #242423;
  text-transform: uppercase;
  letter-spacing: -0.3px;
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

#history-tab .header-content h3::before {
  content: '🚪';
  font-size: 20px;
}

#history-tab .header-subtitle {
  font-size: 12px;
  color: #6F6F6F;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced History List */
#history-tab .history-list {
  background: #FFFFFF;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

#history-tab .history-empty {
  text-align: center;
  color: #6F6F6F;
  font-style: italic;
  padding: 60px 30px;
  font-size: 16px;
  font-weight: 600;
  background: #F4F4F0;
  border-radius: 4px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
}

#history-tab .history-empty::before {
  content: '🚪';
  display: block;
  font-size: 32px;
  margin-bottom: 12px;
}

/* Session Styling */
#history-tab .history-session {
  margin-bottom: 16px;
  background: #F4F4F0;
  border: 3px solid #000000;
  border-radius: 4px;
  box-shadow: 3px 3px 0px #000000;
  transition: all 0.1s ease;
  overflow: hidden;
}

#history-tab .history-session:hover {
  transform: translate(-1px, -1px);
  box-shadow: 4px 4px 0px #000000;
}

#history-tab .history-session.expanded {
  background: #FFFFFF;
}

#history-tab .session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 18px;
  cursor: pointer;
  transition: all 0.1s ease;
}

#history-tab .session-header:hover {
  background: rgba(255, 255, 255, 0.5);
}

#history-tab .session-main-info {
  flex: 1;
}

#history-tab .session-date {
  font-size: 12px;
  color: #6F6F6F;
  margin-bottom: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#history-tab .session-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

#history-tab .session-status {
  font-size: 16px;
  font-weight: 800;
  color: #242423;
  letter-spacing: -0.2px;
}

#history-tab .session-stats {
  display: flex;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
}

#history-tab .stat-success {
  color: #059669;
}

#history-tab .stat-skipped {
  color: #D97706;
}

#history-tab .stat-failed {
  color: #DC2626;
}

#history-tab .session-duration {
  font-size: 11px;
  color: #6F6F6F;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

#history-tab .session-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6F6F6F;
  font-weight: 600;
}

#history-tab .toggle-icon {
  font-size: 14px;
  transition: transform 0.2s ease;
}

#history-tab .history-session.expanded .toggle-icon {
  transform: rotate(180deg);
}

#history-tab .session-details {
  display: none;
  padding: 0 18px 18px 18px;
  border-top: 2px solid #E5E5E5;
  background: #FFFFFF;
}

#history-tab .history-session.expanded .session-details {
  display: block;
}

/* Group Category Styling */
#history-tab .group-category {
  margin-bottom: 16px;
}

#history-tab .group-category:last-child {
  margin-bottom: 0;
}

#history-tab .category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 4px;
  box-shadow: 2px 2px 0px #000000;
}

#history-tab .category-icon {
  font-size: 16px;
  flex-shrink: 0;
}

#history-tab .category-title {
  font-size: 14px;
  font-weight: 700;
  color: #242423;
  text-transform: uppercase;
  letter-spacing: -0.1px;
}

#history-tab .group-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

#history-tab .group-item-detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  background: #FFFFFF;
  border: 2px solid #000000;
  border-radius: 4px;
  box-shadow: 2px 2px 0px #000000;
  transition: all 0.1s ease;
}

#history-tab .group-item-detail:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px #000000;
  background: #F9F9F9;
}

#history-tab .group-name {
  font-size: 13px;
  font-weight: 600;
  color: #242423;
  flex: 1;
  margin-right: 8px;
  word-break: break-word;
}

#history-tab .group-reason {
  font-size: 11px;
  color: #6F6F6F;
  font-weight: 600;
  font-style: italic;
  margin-right: 8px;
  flex-shrink: 0;
}

#history-tab .group-link {
  font-size: 14px;
  text-decoration: none;
  flex-shrink: 0;
  transition: transform 0.1s ease;
}

#history-tab .group-link:hover {
  transform: scale(1.2);
}

#history-tab .no-group-details {
  text-align: center;
  color: #6F6F6F;
  font-style: italic;
  padding: 20px;
  font-size: 14px;
  font-weight: 600;
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 4px;
  box-shadow: 2px 2px 0px #000000;
}

/* License Management Styles - Neubrutalism Design */
.license-section {
  background: #FFFFFF;
  border: 3px solid #000000;
  border-radius: 8px;
  box-shadow: 4px 4px 0px #000000;
  padding: 16px 20px;
  margin-bottom: 16px;
  transition: all 0.1s ease;
}

.license-section:hover {
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
}

.license-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* License Status Display */
.license-status {
  background: #F4F4F0;
  border: 3px solid #000000;
  border-radius: 6px;
  box-shadow: 4px 4px 0px #000000;
  padding: 16px;
  transition: all 0.1s ease;
}

.license-status.active {
  background: linear-gradient(135deg, #F0FDF4 0%, #FFFFFF 100%);
  border-left: 6px solid #23A094;
}

.license-status.inactive {
  background: linear-gradient(135deg, #FEF3F2 0%, #FFFBFA 100%);
  border-left: 6px solid #DC2626;
}

.license-status.none {
  background: #F4F4F0;
  border-left: 6px solid #6B7280;
}

.license-status-active,
.license-status-inactive,
.license-status-none {
  display: flex;
  align-items: center;
  gap: 12px;
}

.license-status-icon {
  font-size: 20px;
  filter: drop-shadow(2px 2px 0px #000000);
  flex-shrink: 0;
}

.license-status-content {
  flex: 1;
}

.license-status-title {
  font-size: 14px;
  font-weight: 800;
  color: #242423;
  margin-bottom: 4px;
  letter-spacing: -0.2px;
}

.license-status-message {
  font-size: 12px;
  font-weight: 600;
  color: #6F6F6F;
  line-height: 1.3;
}

.license-status-key {
  font-size: 11px;
  font-weight: 700;
  color: #23A094;
  font-family: 'Courier New', monospace;
  background: rgba(35, 160, 148, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #23A094;
  margin-top: 4px;
  display: inline-block;
}

/* License Message */
.license-message {
  padding: 12px 16px;
  border-radius: 6px;
  border: 3px solid #000000;
  box-shadow: 4px 4px 0px #000000;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
}

.license-message.success {
  background: linear-gradient(135deg, #F0FDF4 0%, #FFFFFF 100%);
  color: #166534;
  border-color: #23A094;
}

.license-message.error {
  background: linear-gradient(135deg, #FEF3F2 0%, #FFFBFA 100%);
  color: #991B1B;
  border-color: #DC2626;
}

.license-message.info {
  background: linear-gradient(135deg, #F0F9FF 0%, #FFFFFF 100%);
  color: #1E40AF;
  border-color: #3B82F6;
}

/* License Input Section */
.license-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.license-input-group {
  display: flex;
  gap: 8px;
  align-items: stretch;
}

.license-input {
  flex: 1;
  padding: 12px 16px;
  border: 3px solid #000000;
  border-radius: 6px;
  box-shadow: 4px 4px 0px #000000;
  background: #FFFFFF;
  font-size: 12px;
  font-weight: 600;
  color: #242423;
  font-family: 'Courier New', monospace;
  transition: all 0.1s ease;
}

.license-input:focus {
  outline: none;
  transform: translate(-1px, -1px);
  box-shadow: 5px 5px 0px #000000;
  border-color: #FF90E8;
}

.license-input::placeholder {
  color: #6B7280;
  font-weight: 400;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
}

.license-input:disabled {
  background: #F4F4F0;
  color: #6B7280;
  cursor: not-allowed;
}

.license-paste-btn {
  padding: 8px 12px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.license-button-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.license-activate-btn {
  padding: 12px 20px;
  font-size: 13px;
  font-weight: 700;
  flex: 1;
  min-width: 160px;
}

.license-deactivate-btn {
  padding: 12px 16px;
  font-size: 12px;
  font-weight: 600;
}

/* License Instructions */
.license-instructions {
  background: #F4F4F0;
  border: 2px solid #000000;
  border-radius: 6px;
  box-shadow: 2px 2px 0px #000000;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.instruction-icon {
  font-size: 16px;
  filter: drop-shadow(1px 1px 0px #000000);
  flex-shrink: 0;
  margin-top: 2px;
}

.instruction-text {
  font-size: 12px;
  font-weight: 600;
  color: #242423;
  line-height: 1.4;
  flex: 1;
}

/* Print Styles (if needed) */
@media print {
  body {
    width: auto;
    height: auto;
    background: white;
  }

  .container {
    box-shadow: none;
    border: 1px solid #000000;
  }
}