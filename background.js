// Enhanced background.js for Facebook Group Leaver
// Handles tab monitoring, content script management, and real-time communication

class BackgroundManager {
  constructor() {
    this.activeTabId = null;
    this.contentScriptStates = new Map(); // Track content script states per tab
    this.popupPorts = new Set(); // Track active popup connections

    this.setupEventListeners();
    console.log("Enhanced Background service worker started");
  }

  setupEventListeners() {
    // Listen for tab updates (navigation, page changes)
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Listen for tab activation
    chrome.tabs.onActivated.addListener((activeInfo) => {
      this.activeTabId = activeInfo.tabId;
      this.notifyPopupOfTabChange(activeInfo.tabId);
    });

    // Listen for tab removal
    chrome.tabs.onRemoved.addListener((tabId) => {
      this.contentScriptStates.delete(tabId);
    });

    // Handle messages from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Handle popup connections
    chrome.runtime.onConnect.addListener((port) => {
      this.handlePopupConnection(port);
    });
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    // Only process Facebook URLs
    if (!tab.url || !tab.url.includes('facebook.com')) {
      return;
    }

    // Handle URL changes (navigation within Facebook)
    if (changeInfo.url) {
      console.log(`Tab ${tabId} navigated to: ${changeInfo.url}`);
      this.handleUrlChange(tabId, changeInfo.url);
    }

    // Handle page loading completion
    if (changeInfo.status === 'complete') {
      console.log(`Tab ${tabId} finished loading: ${tab.url}`);
      this.handlePageComplete(tabId, tab.url);
    }
  }

  handleUrlChange(tabId, url) {
    // Reset content script state for this tab
    this.contentScriptStates.set(tabId, {
      isReady: false,
      isGroupsPage: this.isGroupsPage(url),
      lastUrl: url,
      timestamp: Date.now()
    });

    // Notify popup of URL change
    this.broadcastToPopups({
      action: 'urlChanged',
      tabId: tabId,
      url: url,
      isGroupsPage: this.isGroupsPage(url)
    });
  }

  handlePageComplete(tabId, url) {
    // Give content script time to initialize
    setTimeout(() => {
      this.checkContentScriptReady(tabId);
    }, 1000);
  }

  async checkContentScriptReady(tabId) {
    try {
      // Try to ping the content script
      const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' });

      if (response && response.status === 'ready') {
        const state = this.contentScriptStates.get(tabId) || {};
        state.isReady = true;
        state.timestamp = Date.now();
        this.contentScriptStates.set(tabId, state);

        // Notify popup that content script is ready
        this.broadcastToPopups({
          action: 'contentScriptReady',
          tabId: tabId,
          isGroupsPage: state.isGroupsPage
        });
      }
    } catch (error) {
      console.log(`Content script not ready on tab ${tabId}:`, error.message);

      // If it's a groups page but content script isn't responding, try to inject it
      const tab = await chrome.tabs.get(tabId);
      if (tab && this.isGroupsPage(tab.url)) {
        this.ensureContentScriptInjected(tabId);
      }
    }
  }

  async ensureContentScriptInjected(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
      console.log(`Content script injected into tab ${tabId}`);

      // Check again after injection
      setTimeout(() => {
        this.checkContentScriptReady(tabId);
      }, 500);
    } catch (error) {
      console.error(`Failed to inject content script into tab ${tabId}:`, error);
    }
  }

  isGroupsPage(url) {
    return url && (
      url.includes('facebook.com/groups/') ||
      url.includes('facebook.com/groups?') ||
      url.includes('facebook.com/groups#')
    );
  }

  handleMessage(message, sender, sendResponse) {
    switch (message.action) {
      case 'updateStatus':
      case 'delayUpdate':
      case 'updateProgress':
      case 'processComplete':
      case 'processError':
      case 'currentAction':
        // Forward content script messages to popup
        this.broadcastToPopups(message);
        break;

      case 'contentScriptReady':
        // Content script announcing it's ready
        if (sender.tab) {
          const state = this.contentScriptStates.get(sender.tab.id) || {};
          state.isReady = true;
          state.timestamp = Date.now();
          this.contentScriptStates.set(sender.tab.id, state);

          this.broadcastToPopups({
            action: 'contentScriptReady',
            tabId: sender.tab.id,
            isGroupsPage: this.isGroupsPage(sender.tab.url)
          });
        }
        break;

      case 'pageContentChanged':
        // Content script detected page content changes
        if (sender.tab) {
          this.broadcastToPopups({
            action: 'pageContentChanged',
            tabId: sender.tab.id,
            data: message.data
          });
        }
        break;

      case 'pageReadinessDetected':
        // Content script detected page is ready with group count
        if (sender.tab) {
          this.broadcastToPopups({
            action: 'pageReadinessDetected',
            tabId: sender.tab.id,
            data: message.data
          });
        }
        break;

      case 'getTabState':
        // Popup requesting current tab state
        this.handleGetTabState(sendResponse);
        break;

      default:
        console.log('Background received message:', message.action);
    }
  }

  async handleGetTabState(sendResponse) {
    try {
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (activeTab) {
        const state = this.contentScriptStates.get(activeTab.id) || {};
        sendResponse({
          tab: activeTab,
          contentScriptReady: state.isReady || false,
          isGroupsPage: this.isGroupsPage(activeTab.url),
          timestamp: state.timestamp || 0
        });
      } else {
        sendResponse({ error: 'No active tab found' });
      }
    } catch (error) {
      sendResponse({ error: error.message });
    }
  }

  handlePopupConnection(port) {
    if (port.name === 'popup') {
      this.popupPorts.add(port);
      console.log('Popup connected');

      port.onDisconnect.addListener(() => {
        this.popupPorts.delete(port);
        console.log('Popup disconnected');
      });
    }
  }

  broadcastToPopups(message) {
    // Send message to all connected popups
    this.popupPorts.forEach(port => {
      try {
        port.postMessage(message);
      } catch (error) {
        // Port might be disconnected, remove it
        this.popupPorts.delete(port);
      }
    });

    // Also try the runtime message API for backwards compatibility
    try {
      chrome.runtime.sendMessage(message);
    } catch (error) {
      // Ignore if no listeners
    }
  }

  notifyPopupOfTabChange(tabId) {
    const state = this.contentScriptStates.get(tabId) || {};
    this.broadcastToPopups({
      action: 'tabActivated',
      tabId: tabId,
      contentScriptReady: state.isReady || false
    });
  }
}

// Initialize the background manager
const backgroundManager = new BackgroundManager();