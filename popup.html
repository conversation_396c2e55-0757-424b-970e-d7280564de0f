<!DOCTYPE html>
<html>
<head>
  <title>Facebook Group Batch Leaver</title>
  <link rel="stylesheet" href="popup.css">
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header class="header">
      <div class="header-icon">🚪</div>
      <h1 class="header-title">Group Leaver</h1>
      <div class="header-subtitle">Batch leave Facebook groups safely</div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="tabs">
      <button class="tab-button active" data-tab="main">Main</button>
      <button class="tab-button" data-tab="settings">Settings</button>
      <button class="tab-button" data-tab="history">History</button>
    </nav>

    <!-- Main Tab Content -->
    <div id="main-tab" class="tab-content active">
      <!-- Hero Status Section -->
      <div class="hero-section">
        <div class="hero-status-card">
          <div class="hero-content">
            <div class="hero-icon" id="statusIcon">🧭</div>
            <div class="hero-text">
              <div class="hero-title" id="statusTitle">Navigation Required</div>
              <div class="hero-message" id="statusMessage">Click below to automatically navigate to Facebook Groups</div>
            </div>
          </div>
          <div class="hero-action">
            <button class="btn btn-hero" id="navigateButton">
              <span class="btn-icon">🚀</span>
              <span class="btn-text">Open Groups Page</span>
            </button>
            <button class="btn btn-premium btn-hero hidden" id="heroUpgradeButton">
              <span class="btn-icon">⭐</span>
              <span class="btn-text">Upgrade to Premium</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Top Progress Indicator (for loading/waiting states) -->
      <div class="top-progress-indicator hidden" id="topProgressIndicator">
        <div class="progress-indicator-content">
          <div class="progress-indicator-icon">🔄</div>
          <div class="progress-indicator-text" id="topProgressText">Loading...</div>
        </div>
        <div class="progress-indicator-bar">
          <div class="progress-indicator-fill" id="topProgressFill"></div>
        </div>
        <div class="progress-indicator-actions">
          <button class="btn btn-danger btn-small" id="stopDiscoveryBtn">Stop Discovery</button>
        </div>
      </div>

      <!-- Groups Overview Section -->
      <div class="groups-overview hidden" id="groupsOverview">
        <div class="overview-header">
          <div class="overview-title">
            <h3>Your Facebook Groups</h3>
          </div>
          <div class="overview-actions">
            <button class="btn btn-secondary btn-compact" id="refreshGroupsBtn">
              <span class="btn-icon">🔄</span>
              <span class="btn-text">Refresh</span>
            </button>
          </div>
        </div>

        <div class="overview-stats">
          <div class="stat-item">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
              <div class="stat-value" id="totalGroupsCount">0</div>
              <div class="stat-label">Total Groups</div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">✅</div>
            <div class="stat-content">
              <div class="stat-value" id="selectedGroupsCount">0</div>
              <div class="stat-label">Selected</div>
            </div>
          </div>
          <div class="stat-item" id="sessionLimitStat">
            <div class="stat-icon">⚡</div>
            <div class="stat-content">
              <div class="stat-value" id="sessionLimit">3</div>
              <div class="stat-label">
                Session Limit
                <span class="premium-feature-indicator unlimited" id="sessionLimitIndicator" style="display: none;">∞</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Session Controls (moved inside groups overview) -->
        <div class="session-controls-compact hidden" id="sessionControlsCompact">
          <div class="control-buttons-inline">
            <button class="btn btn-primary btn-compact" id="startButton" disabled>
              <span class="btn-icon">🚀</span>
              <span class="btn-text">Start Leaving</span>
            </button>
            <button class="btn btn-warning btn-compact hidden" id="pauseButton">
              <span class="btn-icon">⏸️</span>
              <span class="btn-text">Pause</span>
            </button>
            <button class="btn btn-success btn-compact hidden" id="resumeButton">
              <span class="btn-icon">▶️</span>
              <span class="btn-text">Resume</span>
            </button>
            <button class="btn btn-danger btn-compact hidden" id="stopButton">
              <span class="btn-icon">⏹️</span>
              <span class="btn-text">Stop</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Group Selection Section -->
      <div class="group-selection hidden" id="groupSelection">
        <div class="selection-header">
          <h4>Select Groups to Leave</h4>
          <div class="selection-controls">
            <button class="btn btn-secondary btn-compact" id="selectAllBtn">
              <span class="btn-icon">✅</span>
              <span class="btn-text">Select All</span>
            </button>
            <button class="btn btn-secondary btn-compact" id="selectNoneBtn">
              <span class="btn-icon">❌</span>
              <span class="btn-text">Select None</span>
            </button>
          </div>
        </div>

        <!-- Compact Search and Filter Toolbar -->
        <div class="search-filter-toolbar">
          <!-- Search Input -->
          <div class="search-input-wrapper">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" id="groupSearchInput" placeholder="Search groups...">
            <button class="search-clear-btn hidden" id="searchClearBtn" title="Clear search">✕</button>
          </div>

          <!-- Activity Filter (Premium) -->
          <div class="control-group premium-feature" id="activityFilterGroup">
            <select class="filter-select compact" id="activityFilter" title="Activity Filter (Premium) - Filter groups by recent activity status">
              <option value="all">Activity: All</option>
              <option value="active">Activity: Active</option>
              <option value="inactive">Activity: Inactive</option>
            </select>
            <div class="premium-overlay" id="activityFilterOverlay" title="Premium Feature - Click to upgrade for $9 one-time">
              <span class="premium-badge">⭐</span>
            </div>
          </div>

          <!-- Selection Filter (Premium) -->
          <div class="control-group premium-feature" id="selectionFilterGroup">
            <select class="filter-select compact" id="selectionFilter" title="Selection Filter (Premium) - Filter groups by selection status">
              <option value="all">Selection: All</option>
              <option value="selected">Selection: Selected</option>
              <option value="unselected">Selection: Unselected</option>
            </select>
            <div class="premium-overlay" id="selectionFilterOverlay" title="Premium Feature - Click to upgrade for $9 one-time">
              <span class="premium-badge">⭐</span>
            </div>
          </div>

          <!-- Sort by Name (Free Tier) -->
          <div class="control-group">
            <button class="btn btn-sort compact" id="sortNameBtn" title="Sort by name" data-sort="name">
              <span class="btn-icon">🔤</span>
              <span class="sort-indicator" id="sortNameIndicator"></span>
            </button>
          </div>

          <!-- Sort by Activity (Premium) -->
          <div class="control-group premium-feature" id="sortActivityGroup">
            <button class="btn btn-sort compact" id="sortActivityBtn" title="Sort by Date (Premium) - Sort groups by recent activity and last visit dates" data-sort="activity">
              <span class="btn-icon">📅</span>
              <span class="sort-indicator" id="sortActivityIndicator"></span>
            </button>
            <div class="premium-overlay" id="sortActivityOverlay" title="Premium Feature - Click to upgrade for $9 one-time">
              <span class="premium-badge">⭐</span>
            </div>
          </div>

          <!-- Clear All Button -->
          <button class="btn btn-action compact" id="clearFiltersBtn" title="Clear all filters and sorting">
            <span class="btn-icon">🗑️</span>
          </button>
        </div>

        <!-- Filter Status Display -->
        <div class="filter-status hidden" id="filterStatus">
          <div class="filter-status-content">
            <span class="filter-status-text" id="filterStatusText">Showing all groups</span>
            <span class="filter-status-count" id="filterStatusCount">(0 groups)</span>
          </div>
        </div>

        <div class="group-list-container">
          <div class="group-list" id="groupList"></div>
        </div>
      </div>



      <!-- 3-Group Selection Limit Upgrade Prompt -->
      <div class="selection-limit-upgrade-prompt hidden" id="selectionLimitUpgradePrompt">
        <div class="upgrade-prompt-header">
          <div class="upgrade-icon">🔒</div>
          <div class="upgrade-content">
            <h4>Selection limit reached</h4>
            <p>Free plan limited to 3 groups per session. Upgrade for unlimited processing!</p>
          </div>
        </div>
        <div class="upgrade-prompt-actions">
          <button class="btn btn-premium btn-compact" id="upgradeFromLimitBtn">
            <span class="btn-icon">∞</span>
            <span class="btn-text">Upgrade for Unlimited</span>
          </button>
        </div>
        <div class="upgrade-benefits-compact">
          <span class="benefit-text">$9 one-time • No subscription • Unlimited groups</span>
        </div>
      </div>

      <!-- Tab Switch Background Processing Upgrade Prompt -->
      <div class="tab-switch-upgrade-prompt hidden" id="tabSwitchUpgradePrompt">
        <div class="upgrade-prompt-header">
          <div class="upgrade-icon">🔄</div>
          <div class="upgrade-content">
            <h4>Processing paused</h4>
            <p>Premium users can switch tabs while processing continues in background</p>
          </div>
        </div>
        <div class="upgrade-prompt-actions">
          <button class="btn btn-premium btn-compact" id="upgradeFromTabSwitchBtn">
            <span class="btn-icon">🚀</span>
            <span class="btn-text">Enable Background Processing</span>
          </button>
        </div>
        <div class="upgrade-benefits-compact">
          <span class="benefit-text">$9 one-time • Buy once, use forever</span>
        </div>
      </div>



      <!-- Progress Section -->
      <div class="progress-section hidden" id="progressSection">
        <div class="progress-header">
          <h4>Processing Progress</h4>
          <div class="progress-stats">
            <span id="progressStats">0 / 0 processed</span>
            <span id="progressTime"></span>
          </div>
        </div>

        <div class="progress-bar-container">
          <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
          </div>
          <div class="progress-percentage" id="progressPercentage">0%</div>
        </div>

        <div class="progress-details">
          <div class="progress-item">
            <span class="progress-label">✅ Left:</span>
            <span class="progress-value" id="leftCount">0</span>
          </div>
          <div class="progress-item">
            <span class="progress-label">⏭️ Skipped:</span>
            <span class="progress-value" id="skippedCount">0</span>
          </div>
          <div class="progress-item">
            <span class="progress-label">❌ Failed:</span>
            <span class="progress-value" id="failedCount">0</span>
          </div>
        </div>

        <div class="current-action" id="currentAction"></div>
      </div>

      <!-- Debug Actions (Hidden by default) -->
      <div class="debug-section hidden">
        <button class="btn btn-secondary btn-small" id="debugGroupsBtn">🔍 Debug Groups</button>
      </div>
    </div>

    <!-- Settings Tab Content -->
    <div id="settings-tab" class="tab-content">
      <!-- Essential Settings Section - Moved to top -->
      <div class="essential-settings-section">
        <div class="section-header">
          <h3>Essential Settings</h3>
          <div class="section-subtitle">Safety features available in all plans</div>
        </div>

        <div class="essential-settings-grid">
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="skipAdminGroups" checked>
              <span class="checkmark"></span>
              <div class="checkbox-content">
                <div class="checkbox-title">Skip Admin Groups</div>
                <div class="checkbox-description">Never leave groups where you're an administrator</div>
              </div>
            </label>
          </div>

          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="confirmBeforeStart" checked>
              <span class="checkmark"></span>
              <div class="checkbox-content">
                <div class="checkbox-title">Confirm Before Starting</div>
                <div class="checkbox-description">Show confirmation dialog before processing begins</div>
              </div>
            </label>
          </div>
        </div>


      </div>

      <!-- License Management Section -->
      <div class="license-section">
        <div class="section-header">
          <h3>Premium License</h3>
          <div class="section-subtitle">Activate your license key to unlock premium features</div>
        </div>

        <div class="license-container">
          <!-- License Status Display -->
          <div id="licenseStatus" class="license-status none">
            <div class="license-status-none">
              <div class="license-status-icon">ℹ️</div>
              <div class="license-status-content">
                <div class="license-status-title">No License Activated</div>
                <div class="license-status-message">Enter your license key to unlock premium features</div>
              </div>
            </div>
          </div>

          <!-- License Message Area -->
          <div id="licenseMessage" class="license-message" style="display: none;"></div>

          <!-- License Input Section -->
          <div class="license-input-section">
            <div class="license-input-group">
              <input
                type="text"
                id="licenseKeyInput"
                class="license-input"
                placeholder="Enter your license key (e.g., XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX)"
                maxlength="35"
              >
              <button id="licensePasteBtn" class="btn btn-secondary btn-small license-paste-btn" title="Paste from clipboard">
                📋
              </button>
            </div>

            <div class="license-button-group">
              <button id="licenseActivateBtn" class="btn btn-premium license-activate-btn" disabled>
                <span class="btn-icon">🔑</span>
                Activate License
              </button>
              <button id="licenseDeactivateBtn" class="btn btn-secondary license-deactivate-btn" style="display: none;">
                Deactivate
              </button>
            </div>
          </div>

          <!-- License Instructions -->
          <div class="license-instructions">
            <div class="instruction-item">
              <div class="instruction-icon">1️⃣</div>
              <div class="instruction-text">Purchase your license from Gumroad using the upgrade button below</div>
            </div>
            <div class="instruction-item">
              <div class="instruction-icon">2️⃣</div>
              <div class="instruction-text">Copy your license key from the Gumroad purchase confirmation</div>
            </div>
            <div class="instruction-item">
              <div class="instruction-icon">3️⃣</div>
              <div class="instruction-text">Paste the license key above and click "Activate License"</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Progressive Tier Comparison - Restructured Format -->
      <div class="comparison-container">
        <!-- Free Tier Column -->
        <div class="tier-column tier-free">
          <div class="tier-header">
            <div class="tier-badge tier-badge-free">Free</div>
            <div class="tier-title">Basic Group Leaving</div>
            <div class="tier-price">$0<span class="tier-period"> forever</span></div>
            <div class="tier-description">Perfect for occasional use</div>
          </div>

          <div class="tier-features">
            <div class="feature-section-header">
              <div class="feature-section-title">What's Included:</div>
            </div>

            <div class="feature-item feature-included">
              <div class="feature-icon">✓</div>
              <div class="feature-content">
                <div class="feature-title">Manual Scroll Controls</div>
                <div class="feature-description">You control when to load more groups</div>
              </div>
            </div>

            <div class="feature-item feature-included">
              <div class="feature-icon">✓</div>
              <div class="feature-content">
                <div class="feature-title">3 Groups Per Session Limit</div>
                <div class="feature-description">Process up to 3 groups at a time</div>
              </div>
            </div>

            <div class="feature-item feature-included">
              <div class="feature-icon">✓</div>
              <div class="feature-content">
                <div class="feature-title">Slow Processing with Delays</div>
                <div class="feature-description">Safe, deliberate group leaving</div>
              </div>
            </div>

            <div class="feature-item feature-included">
              <div class="feature-icon">✓</div>
              <div class="feature-content">
                <div class="feature-title">Essential Safety Features</div>
                <div class="feature-description">Admin protection & confirmations</div>
              </div>
            </div>

            <div class="feature-item feature-included">
              <div class="feature-icon">✓</div>
              <div class="feature-content">
                <div class="feature-title">Basic Search & Sort</div>
                <div class="feature-description">Search by name, sort A-Z</div>
              </div>
            </div>
          </div>

          <div class="tier-cta">
            <button class="btn btn-secondary btn-tier" disabled>
              Current Plan
            </button>
          </div>
        </div>

        <!-- Premium Tier Column -->
        <div class="tier-column tier-premium">
          <div class="tier-header">
            <div class="tier-badge tier-badge-premium">Premium</div>
            <div class="tier-title">Unlimited Group Leaving</div>
            <div class="tier-price">$9<span class="tier-period"> one-time</span></div>
            <div class="tier-description">
              <strong>Buy once, use forever</strong>
            </div>
          </div>

          <div class="tier-features">
            <div class="feature-section-header">
              <div class="feature-section-title">All freemium benefits PLUS:</div>
            </div>

            <div class="feature-item feature-premium">
              <div class="feature-icon">⚡</div>
              <div class="feature-content">
                <div class="feature-title">Automatic Scrolling</div>
                <div class="feature-description">Intelligent auto-discovery of all groups</div>
              </div>
            </div>

            <div class="feature-item feature-premium">
              <div class="feature-icon">∞</div>
              <div class="feature-content">
                <div class="feature-title">Unlimited Groups Per Session</div>
                <div class="feature-description">Process as many groups as you want</div>
              </div>
            </div>

            <div class="feature-item feature-premium">
              <div class="feature-icon">🚀</div>
              <div class="feature-content">
                <div class="feature-title">Faster Processing</div>
                <div class="feature-description">No delays between group leaving</div>
              </div>
            </div>

            <div class="feature-item feature-premium">
              <div class="feature-icon">🔍</div>
              <div class="feature-content">
                <div class="feature-title">Advanced Search & Filters</div>
                <div class="feature-description">Filter by activity status, selection state</div>
              </div>
            </div>

            <div class="feature-item feature-premium">
              <div class="feature-icon">📅</div>
              <div class="feature-content">
                <div class="feature-title">Smart Activity Sorting</div>
                <div class="feature-description">Sort by recent activity and engagement</div>
              </div>
            </div>
          </div>

          <div class="tier-cta">
            <button class="btn btn-premium btn-tier" id="upgradeBtn">
              <span class="btn-icon">⭐</span>
              Upgrade to Premium
            </button>
          </div>
        </div>
      </div>

      <!-- Hidden Premium Feature Checkboxes (for JavaScript compatibility) -->
      <div style="display: none;">
        <input type="checkbox" id="autoScroll">
        <input type="checkbox" id="skipRecentlyActive">
        <input type="checkbox" id="retryFailedGroups">
        <input type="checkbox" id="detailedAnalytics">
      </div>
    </div>

    <!-- History Tab Content -->
    <div id="history-tab" class="tab-content">
      <div class="history-container">
        <div class="history-header">
          <div class="header-content">
            <h3>Leaving Sessions</h3>
            <div class="header-subtitle">Detailed history of your group leaving activities</div>
          </div>
          <button class="btn btn-secondary btn-small" id="clearHistoryBtn">Clear History</button>
        </div>
        <div class="history-list" id="historyList">
          <div class="history-empty">No leaving sessions yet</div>
        </div>


      </div>
    </div>
  </div>

  <script src="license-manager.js"></script>
  <script src="popup.js"></script>
</body>
</html>